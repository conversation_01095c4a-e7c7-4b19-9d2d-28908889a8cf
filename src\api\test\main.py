"""
API-TEST: 8 Core Endpoints for CAD Generation System
This is the test API with simplified, focused endpoints.
"""

import os
import logging
from typing import List, Optional

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tolery-api-test")

# Database and model imports
try:
    from ...database.database import get_db
    from ...models.sessions import Session as SessionModel, ChatHistory
    from ... import crud
    from ...core.chatbot import text_to_cad_agent
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import get_db
    from src.models.sessions import Session as SessionModel, ChatHistory
    import src.crud as crud
    from src.core.chatbot import text_to_cad_agent

# FastAPI app (no lifespan when mounted as sub-app)
app = FastAPI(
    title="Tolery API-TEST",
    description="8 Core Endpoints for CAD Generation System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Response Models
class SessionInfo(BaseModel):
    """Session information response model."""
    session_id: str = Field(..., description="Session ID")
    created_at: str = Field(..., description="Session creation timestamp")
    updated_at: str = Field(..., description="Session last update timestamp")

    class Config:
        from_attributes = True

class SessionInfoResponse(BaseModel):
    """Detailed session information response."""
    session_id: str = Field(..., description="Session ID")
    created_at: str = Field(..., description="Session creation timestamp")
    updated_at: str = Field(..., description="Session last update timestamp")
    message_count: int = Field(..., description="Number of messages in session")
    latest_code: Optional[str] = Field(None, description="Latest generated code")

class ExportResponse(BaseModel):
    """Export files response model."""
    session_id: str = Field(..., description="Session ID")
    obj_exports: List[str] = Field(default_factory=list, description="OBJ file URLs")
    step_exports: List[str] = Field(default_factory=list, description="STEP file URLs")
    total_exports: int = Field(..., description="Total number of exports")

class ChatHistoryResponse(BaseModel):
    """Chat history response model."""
    session_id: str = Field(..., description="Session ID")
    messages: List[dict] = Field(..., description="List of chat messages")
    total_messages: int = Field(..., description="Total number of messages")

class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(..., description="The user's message/request")
    image_path: str = Field("", description="Path to the uploaded image (if any)")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")
    part_file_name: str = Field("part_file_name", description="Name of the part file")
    export_format: str = Field("obj", description="Format for export (obj, step, dxf)")
    material_choice: str = Field("STEEL", description="Material choice for the part")
    selected_feature_uuid: str = Field("", description="UUID of selected feature (if any)")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")

class ChatResponse(BaseModel):
    """Chat response model."""
    message: str = Field(..., description="Response message")
    session_id: str = Field(..., description="Session ID")
    obj_export: Optional[str] = Field(None, description="OBJ file URL if generated")
    step_export: Optional[str] = Field(None, description="STEP file URL if generated")
    error: Optional[str] = Field(None, description="Error message if any")

# 1. LIST SESSIONS
@app.get("/api/sessions", response_model=List[SessionInfo])
async def list_sessions(db: Session = Depends(get_db)):
    """List all available sessions."""
    try:
        sessions = crud.get_all_sessions(db)
        # Convert datetime objects to strings
        session_list = []
        for session in sessions:
            session_info = SessionInfo(
                session_id=session.session_id,
                created_at=session.created_at.isoformat(),
                updated_at=session.updated_at.isoformat()
            )
            session_list.append(session_info)
        return session_list
    except SQLAlchemyError as e:
        logger.error(f"Database error when listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving sessions")
    except Exception as e:
        logger.error(f"Unexpected error when listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

# 2. GET SESSION INFO
@app.get("/api/sessions/{session_id}", response_model=SessionInfoResponse)
async def get_session_info(session_id: str, db: Session = Depends(get_db)):
    """Get detailed information about a specific session."""
    try:
        # Get session
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get chat history count
        message_count = db.query(ChatHistory).filter(ChatHistory.session_id == session_id).count()

        # Get latest code
        latest_entry = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.lasted_code.isnot(None)
        ).order_by(ChatHistory.id.desc()).first()

        latest_code = latest_entry.lasted_code if latest_entry else None

        return SessionInfoResponse(
            session_id=session.session_id,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat(),
            message_count=message_count,
            latest_code=latest_code
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting session info: {str(e)}")

# 3. DELETE SESSION
@app.delete("/api/sessions/{session_id}")
async def delete_session(session_id: str, db: Session = Depends(get_db)):
    """Delete a specific session and its chat history."""
    try:
        # Check if session exists
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Delete chat history first (due to foreign key constraints)
        db.query(ChatHistory).filter(ChatHistory.session_id == session_id).delete()

        # Delete session
        db.delete(session)
        db.commit()

        return {"message": f"Session {session_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

# 4. GET EXPORT FILES
@app.get("/api/get-export", response_model=ExportResponse)
async def get_export(session_id: str, types: Optional[str] = None, db: Session = Depends(get_db)):
    """Get export files for a session."""
    try:
        # Get chat history with exports
        query = db.query(ChatHistory).filter(ChatHistory.session_id == session_id)

        if types:
            # Filter by export types
            from sqlalchemy import or_
            type_list = [t.strip().lower() for t in types.split(',')]
            filters = []
            if 'obj' in type_list:
                filters.append(ChatHistory.obj_export.isnot(None))
            if 'step' in type_list:
                filters.append(ChatHistory.step_export.isnot(None))

            if filters:
                query = query.filter(or_(*filters))

        exports = query.all()

        obj_exports = [entry.obj_export for entry in exports if entry.obj_export]
        step_exports = [entry.step_export for entry in exports if entry.step_export]

        return ExportResponse(
            session_id=session_id,
            obj_exports=obj_exports,
            step_exports=step_exports,
            total_exports=len(obj_exports) + len(step_exports)
        )

    except Exception as e:
        logger.error(f"Error getting exports: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting exports: {str(e)}")

# 5. GET CHAT HISTORY
@app.get("/api/chat-history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """Retrieve chat history for any session (PDF, Image, or Chat)."""
    logger.info(f"Getting chat history for session: {session_id}")

    try:
        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        if not chat_entries:
            return ChatHistoryResponse(
                session_id=session_id,
                messages=[],
                total_messages=0
            )

        # Convert to conversation format with role-based messages
        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append({
                    "timestamp": entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.created_at else None,
                    "role": "human",
                    "content": entry.message
                })

            # Add AI response if available
            if entry.response:
                ai_msg = {
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.response
                }
                # Add export information to AI response if available
                if entry.obj_export or entry.step_export:
                    ai_msg["exports"] = {
                        "obj": entry.obj_export,
                        "step": entry.step_export
                    }
                messages.append(ai_msg)
            elif entry.output:
                # Fallback to output if no response
                ai_msg = {
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.output
                }
                # Add export information to AI response if available
                if entry.obj_export or entry.step_export:
                    ai_msg["exports"] = {
                        "obj": entry.obj_export,
                        "step": entry.step_export
                    }
                messages.append(ai_msg)

        return ChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")

# 6. CHAT (Regular text chat)
@app.post("/api/chat", response_model=ChatResponse)
async def chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """Process a chat message and generate CAD code with session continuity."""
    user_message = request_data.message
    session_id = request_data.session_id
    
    logger.info(f"Chat request received: '{user_message[:50]}...' (session_id: {session_id})")

    if not user_message:
        logger.warning("Empty message received")
        raise HTTPException(status_code=400, detail="No message provided")

    try:
        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=user_message,
            session_id=session_id,
            image_path=request_data.image_path,
            part_file_name=request_data.part_file_name,
            export_format=request_data.export_format,
            material_choice=request_data.material_choice,
            selected_feature_uuid=request_data.selected_feature_uuid,
            is_edit_request=request_data.is_edit_request
        )

        # Process using our enhanced session handling in CRUD
        result = crud.chat_processing.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='web')

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing chat request: {result['error']}")
            return ChatResponse(
                message=result.get("message", "An error occurred"),
                session_id=session_id or "unknown",
                error=result["error"]
            )

        # Return successful response
        return ChatResponse(
            message=result.chat_response,
            session_id=result.session_id,
            obj_export=result.obj_export,
            step_export=result.step_export
        )

    except Exception as e:
        logger.exception(f"Unexpected error in chat endpoint: {str(e)}")
        return ChatResponse(
            message="An error occurred while processing your request",
            session_id=session_id or "unknown",
            error=str(e)
        )

# Edit mode endpoint
@app.post("/api/edit", tags=["cad"])
async def edit_chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """Process an edit request to modify existing CAD code."""
    user_message = request_data.message
    session_id = request_data.session_id
    
    logger.info(f"Edit request received: '{user_message[:50]}...' (session_id: {session_id})")

    if not user_message:
        logger.warning("Empty message received")
        raise HTTPException(status_code=400, detail="No message provided")
        
    if not session_id:
        logger.warning("No session ID provided for edit request")
        raise HTTPException(status_code=400, detail="Session ID is required for edit mode")

    try:
        # First check if code exists for this session
        latest_code = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.lasted_code.isnot(None)
        ).order_by(ChatHistory.created_at.desc()).first()
        
        if not latest_code or not latest_code.lasted_code:
            error_msg = "No code has been generated in this session yet. Generate code first before using edit mode."
            logger.warning(f"{error_msg} Session ID: {session_id}")
            return ChatResponse(
                message=error_msg,
                session_id=session_id,
                error=error_msg
            )
        
        # Set is_edit_request to True and call the chat endpoint
        request_data.is_edit_request = True
        return await chat(request_data, db)

    except Exception as e:
        logger.exception(f"Unexpected error in edit endpoint: {str(e)}")
        return ChatResponse(
            message="An error occurred while processing your edit request",
            session_id=session_id or "unknown",
            error=str(e)
        )

# Import and mount PDF and Image Chat routes (endpoints 7 & 8)
try:
    from .pdf_chat import router as pdf_chat_router
    from .image_chat import router as image_chat_router
except ImportError:
    # Fallback to local files in test directory
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    from pdf_chat import router as pdf_chat_router
    from image_chat import router as image_chat_router

app.include_router(pdf_chat_router, prefix="/api", tags=["pdf-chat"])
app.include_router(image_chat_router, prefix="/api", tags=["image-chat"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8125)
