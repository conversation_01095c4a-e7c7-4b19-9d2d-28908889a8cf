"""
Image Chat Router for API-TEST
Handles image upload and processing with CAD generation.
"""

import os
import tempfile
import logging
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

# Configure logging
logger = logging.getLogger(__name__)

# Database and model imports
try:
    from ...database.database import get_db
    from ...models.sessions import Session as SessionModel, ChatHistory
    from ...crud import chat_processing as crud
    from ...core.chatbot import text_to_cad_agent
    from ...utils.image_handler import ImageProcessor
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import get_db
    from src.models.sessions import Session as SessionModel, ChatHistory
    from src.crud import chat_processing as crud
    from src.core.chatbot import text_to_cad_agent
    from src.utils.image_handler import ImageProcessor

# Initialize image processor
image_processor = ImageProcessor(cad_agent=text_to_cad_agent)

# Create router
router = APIRouter(
    prefix="/chat-image",
    tags=["image-chat"],
    responses={404: {"description": "Not found"}},
)

# Request/Response Models
class ImageChatRequest(BaseModel):
    """Image chat request model."""
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

class ImageChatResponse(BaseModel):
    """Image chat response model."""
    success: bool = Field(..., description="Whether the processing was successful")
    user_message: str = Field(..., description="User input message")
    response: str = Field(..., description="Toolchat response message")
    session_id: str = Field(..., description="Session ID")
    obj_export: Optional[str] = Field(None, description="OBJ file URL if generated")
    step_export: Optional[str] = Field(None, description="STEP file URL if generated")
    error: Optional[str] = Field(None, description="Error message if any")

@router.post("/", response_model=ImageChatResponse)
async def chat_image(
    file: UploadFile = File(..., description="Image file to process"),
    session_id: Optional[str] = Form(None, description="Optional session ID - will generate if not provided"),
    user_input: str = Form("", description="Additional user input or instructions"),
    db: Session = Depends(get_db)
):
    """
    Process an image file and generate CAD code.

    Workflow:
    1. Upload image file
    2. Analyze image to extract class/name information
    3. Pass analysis results to chat tool
    4. Generate code/obj/step files like normal chat functionality
    """
    logger.info(f"Image chat request received: {file.filename} (session_id: {session_id})")

    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = Path(file.filename).suffix.lower()

    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400,
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )

    try:
        # Process the image using the ImageProcessor, passing the session_id
        success, analysis_result, final_session_id = await image_processor.process_uploaded_file(db, file, user_input, session_id)

        # Use the final session_id (either provided or generated)
        session_id = final_session_id

        if not success:
            logger.error(f"Image processing failed: {analysis_result}")
            return ImageChatResponse(
                success=False,
                user_message=user_input,
                response=analysis_result,
                session_id=session_id or "unknown",
                error="Image processing failed"
            )

        # Create message for chat tool based on image analysis
        chat_message = f"Based on image analysis: {analysis_result}. {user_input if user_input else ''}"

        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=chat_message,
            session_id=session_id,
            image_path="",
            part_file_name="part_file_name",
            export_format="obj",
            material_choice="STEEL",
            selected_feature_uuid="",
            is_edit_request=False
        )

        # Process using chat tool
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='image')

        # Override the user message in database to save only user input, not full analysis
        if hasattr(result, 'session_id') and result.session_id:
            # Update the latest chat history entry to have user_input as message instead of full analysis
            try:
                from ...models.sessions import ChatHistory
                latest_entry = db.query(ChatHistory).filter(
                    ChatHistory.session_id == result.session_id
                ).order_by(ChatHistory.id.desc()).first()

                if latest_entry:
                    latest_entry.message = user_input  # Override with user input only
                    db.commit()
                    logger.info(f"Updated chat history message to user input: {user_input}")
            except Exception as e:
                logger.warning(f"Failed to update chat history message: {e}")

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing image chat request: {result['error']}")
            return ImageChatResponse(
                success=False,
                user_message=user_input,
                response=result.get("message", "An error occurred"),
                session_id=session_id or "unknown",
                error=result["error"]
            )

        # Return successful response
        return ImageChatResponse(
            success=True,
            user_message=user_input,
            response=result.chat_response,
            session_id=result.session_id,
            obj_export=result.obj_export,
            step_export=result.step_export
        )

    except Exception as e:
        logger.error(f"Error processing image chat request: {e}")
        return ImageChatResponse(
            success=False,
            user_message=user_input,
            response="An error occurred while processing the image",
            session_id=session_id or "unknown",
            error=str(e)
        )
