# -*- coding: utf-8 -*-
"""
API routes for PDF and image processing and other utilities.
"""

import os
import tempfile
import logging
from pathlib import Path

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional

from ..utils.pdf_handler import PDFProcessor
from ..utils.image_handler import ImageProcessor

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api",
    tags=["utilities"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad request"}},
)

# Initialize processors
pdf_processor = PDFProcessor()
image_processor = ImageProcessor()


class PDFResponse(BaseModel):
    """
    Response model for PDF processing.
    """
    success: bool
    message: str


class ImageResponse(BaseModel):
    """
    Response model for image processing.
    """
    success: bool
    message: str


@router.post("/process-pdf", response_model=PDFResponse)
async def process_pdf(file: UploadFile = File(...), user_input: str = Form("")):
    """
    Process a PDF file with OpenAI API.
    
    Args:
        file: Uploaded PDF file
        user_input: Optional user input to send along with the default prompt
        
    Returns:
        PDFResponse: Response with success status and message
    """
    logger.info(f"Received PDF processing request: {file.filename}")
    
    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")
    
    try:
        # Create a temporary file to store the uploaded PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp:
            # Write the file content
            content = await file.read()
            temp.write(content)
            temp_path = temp.name
        
        logger.info(f"PDF saved to temporary file: {temp_path}")
        
        # Process the PDF
        success, response = pdf_processor.process_pdf(temp_path, user_input)
        
        # Clean up temporary file
        try:
            os.unlink(temp_path)
            logger.info(f"Removed temporary file: {temp_path}")
        except Exception as e:
            logger.warning(f"Failed to remove temporary file: {e}")
        
        return PDFResponse(success=success, message=response)
    
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")


@router.post("/process-image", response_model=ImageResponse)
async def process_image(file: UploadFile = File(...), user_input: str = Form("")):
    """
    Process an image file with OpenAI Vision API.
    
    Args:
        file: Uploaded image file (JPG, JPEG, PNG, GIF, BMP, TIFF, WEBP)
        user_input: Optional user input to send along with the default prompt
        
    Returns:
        ImageResponse: Response with success status and message
    """
    logger.info(f"Received image processing request: {file.filename}")
    
    # Validate file is a supported image format
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in supported_extensions:
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(
            status_code=400, 
            detail=f"File must be an image. Supported formats: {', '.join(supported_extensions)}"
        )
    
    try:
        # Process the image using the ImageProcessor
        success, response = await image_processor.process_uploaded_file(file, user_input)
        
        return ImageResponse(success=success, message=response)
    
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")


@router.post("/process-multi-file", response_model=dict)
async def process_multi_file(
    pdf_file: Optional[UploadFile] = File(None),
    image_file: Optional[UploadFile] = File(None),
    user_input: str = Form("")
):
    """
    Process multiple files (PDF and/or image) together.
    
    Args:
        pdf_file: Optional uploaded PDF file
        image_file: Optional uploaded image file
        user_input: Optional user input to send along with the default prompt
        
    Returns:
        dict: Combined response with results from both file types
    """
    if not pdf_file and not image_file:
        raise HTTPException(status_code=400, detail="At least one file (PDF or image) must be provided")
    
    results = {}
    
    # Process PDF if provided
    if pdf_file:
        logger.info(f"Processing PDF in multi-file request: {pdf_file.filename}")
        try:
            success, response = await pdf_processor.process_uploaded_file(pdf_file, user_input)
            results["pdf"] = {"success": success, "message": response}
        except Exception as e:
            logger.error(f"Error processing PDF in multi-file request: {e}")
            results["pdf"] = {"success": False, "message": f"Error processing PDF: {str(e)}"}
    
    # Process image if provided
    if image_file:
        logger.info(f"Processing image in multi-file request: {image_file.filename}")
        try:
            success, response = await image_processor.process_uploaded_file(image_file, user_input)
            results["image"] = {"success": success, "message": response}
        except Exception as e:
            logger.error(f"Error processing image in multi-file request: {e}")
            results["image"] = {"success": False, "message": f"Error processing image: {str(e)}"}
    
    # Combine results if both files were processed
    if pdf_file and image_file:
        pdf_success = results.get("pdf", {}).get("success", False)
        image_success = results.get("image", {}).get("success", False)
        
        if pdf_success and image_success:
            combined_message = f"PDF Analysis: {results['pdf']['message']}\n\nImage Analysis: {results['image']['message']}"
            results["combined"] = {"success": True, "message": combined_message}
        else:
            results["combined"] = {"success": False, "message": "One or more files failed to process"}
    
    return results
