"""
Comprehensive logging configuration for the DFM Shape ChatBot application.
This module provides structured logging with different levels and formatters
to improve debugging and monitoring capabilities.
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels."""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green  
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Add color to the levelname
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class ProcessFlowFilter(logging.Filter):
    """Filter to identify and categorize process flow logs."""
    
    FLOW_CATEGORIES = [
        'FLOW', 'SESSION', 'AGENT', 'STREAM', 'EXPORT', 'WEB_SEARCH',
        'EDIT_MODE', 'RESULT_PROCESS', 'CHAT_HISTORY', 'SSE'
    ]
    
    def filter(self, record):
        # Add flow category if present in message
        if hasattr(record, 'msg'):
            msg = str(record.msg)
            for category in self.FLOW_CATEGORIES:
                if f"[{category}" in msg:
                    record.flow_category = category
                    break
            else:
                record.flow_category = "GENERAL"
        
        return True


def setup_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    enable_file_logging: bool = True,
    enable_console_logging: bool = True,
    enable_flow_logging: bool = True,
    max_log_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
):
    """
    Setup comprehensive logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files
        enable_file_logging: Whether to enable file logging
        enable_console_logging: Whether to enable console logging
        enable_flow_logging: Whether to enable separate flow logging
        max_log_size: Maximum size of each log file in bytes
        backup_count: Number of backup log files to keep
    """
    
    # Create log directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Clear any existing handlers
    logging.getLogger().handlers.clear()
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Detailed formatter for files
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Simple formatter for console
    console_formatter = ColoredFormatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Flow-specific formatter
    flow_formatter = logging.Formatter(
        fmt='%(asctime)s - [%(flow_category)s] - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    handlers = []
    
    # Console handler
    if enable_console_logging:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(console_formatter)
        handlers.append(console_handler)
    
    # Main application log file handler
    if enable_file_logging:
        main_log_file = log_path / "application.log"
        file_handler = logging.handlers.RotatingFileHandler(
            filename=main_log_file,
            maxBytes=max_log_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(detailed_formatter)
        handlers.append(file_handler)
    
    # Process flow log file handler
    if enable_flow_logging:
        flow_log_file = log_path / "process_flow.log"
        flow_handler = logging.handlers.RotatingFileHandler(
            filename=flow_log_file,
            maxBytes=max_log_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        flow_handler.setLevel(numeric_level)
        flow_handler.setFormatter(flow_formatter)
        flow_handler.addFilter(ProcessFlowFilter())
        handlers.append(flow_handler)
    
    # Error-only log file handler
    if enable_file_logging:
        error_log_file = log_path / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            filename=error_log_file,
            maxBytes=max_log_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        handlers.append(error_handler)
    
    # Add all handlers to root logger
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # Set specific logger levels for noisy libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # Log the configuration
    root_logger.info(f"[LOGGING] Logging configured - Level: {log_level}, File: {enable_file_logging}, Console: {enable_console_logging}")
    root_logger.info(f"[LOGGING] Log directory: {log_path.absolute()}")
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def log_function_entry(logger: logging.Logger, func_name: str, **kwargs):
    """
    Log function entry with parameters.
    
    Args:
        logger: Logger instance
        func_name: Function name
        **kwargs: Function parameters to log
    """
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.debug(f"[FUNC_ENTRY] {func_name}({params})")


def log_function_exit(logger: logging.Logger, func_name: str, duration: float = None, result_summary: str = None):
    """
    Log function exit with optional duration and result summary.
    
    Args:
        logger: Logger instance
        func_name: Function name
        duration: Function execution duration in seconds
        result_summary: Summary of the result
    """
    msg_parts = [f"[FUNC_EXIT] {func_name}()"]
    if duration is not None:
        msg_parts.append(f"duration={duration:.3f}s")
    if result_summary:
        msg_parts.append(f"result={result_summary}")
    
    logger.debug(" - ".join(msg_parts))


def log_performance(logger: logging.Logger, operation: str, duration: float, details: str = None):
    """
    Log performance metrics for operations.
    
    Args:
        logger: Logger instance
        operation: Operation name
        duration: Operation duration in seconds
        details: Additional details
    """
    msg = f"[PERFORMANCE] {operation}: {duration:.3f}s"
    if details:
        msg += f" - {details}"
    
    logger.info(msg)


def log_session_state(logger: logging.Logger, session_id: str, state: dict):
    """
    Log session state information in a structured way.
    
    Args:
        logger: Logger instance
        session_id: Session ID
        state: Session state dictionary
    """
    logger.info(f"[SESSION_STATE] {session_id} - State: {state.get('conversation_state', 'unknown')}")
    logger.info(f"[SESSION_STATE] {session_id} - History count: {len(state.get('user_text_history', []))}")
    logger.info(f"[SESSION_STATE] {session_id} - Responses count: {len(state.get('previous_responses', []))}")
    
    if state.get('latest_requirements'):
        req = state['latest_requirements']
        logger.info(f"[SESSION_STATE] {session_id} - Latest requirements: {getattr(req, 'title', 'No title')}")
        logger.info(f"[SESSION_STATE] {session_id} - Missing info: {getattr(req, 'missing_info', False)}")


def log_agent_result(logger: logging.Logger, session_id: str, result: dict):
    """
    Log agent result in a structured way.
    
    Args:
        logger: Logger instance
        session_id: Session ID
        result: Agent result dictionary
    """
    logger.info(f"[AGENT_RESULT] {session_id} - Has code: {bool(result.get('code'))}")
    logger.info(f"[AGENT_RESULT] {session_id} - Has message: {bool(result.get('message'))}")
    logger.info(f"[AGENT_RESULT] {session_id} - Has error: {bool(result.get('error'))}")
    
    if result.get('code'):
        logger.info(f"[AGENT_RESULT] {session_id} - Code length: {len(result['code'])} characters")
    
    if result.get('gltf_path'):
        logger.info(f"[AGENT_RESULT] {session_id} - GLTF path: {result['gltf_path']}")
    
    if result.get('obj_path'):
        logger.info(f"[AGENT_RESULT] {session_id} - OBJ path: {result['obj_path']}")
    
    if result.get('step_path'):
        logger.info(f"[AGENT_RESULT] {session_id} - STEP path: {result['step_path']}")


# Configure UTF-8 encoding for Windows compatibility
if sys.platform.startswith('win'):
    import codecs
    if not hasattr(sys.stdout, 'encoding') or sys.stdout.encoding.lower() != 'utf-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except AttributeError:
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict') 