import os
import sys
import json
import re
import subprocess
import time
import traceback
from pathlib import Path
from typing import List, Optional, Dict, Any
import logging

# Import from local modules
from .models import (
    ShapeRequirement, ExtractedShapeInfo, Operation, 
    DesignRequirements, AnalysisAndParameterCheckOutput
)
from .agent_utils import (
    parse_unified_analysis, parse_unified_output, clean_code,
    create_rag_query, format_retrieved_context, detect_language,
    get_success_message, combine_and_format_contexts,
    get_dynamic_guidance_from_dictionary
)
from .agent_chains import (
    create_unified_processing_chain,
    create_code_generation_chain,
    create_code_editing_chain
)

from src.utils import path_manager
from src.rag.retriever import set_classification_llm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure UTF-8 encoding
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# Define model choices (kept for reference, actual models passed in)
MODELS = {
    "default": "o4-mini-2025-04-16",
    "advanced": "o4-mini-2025-04-16",
    "expert": "o4-mini-2025-04-16",
}

# --- Local RAG Setup Paths (Adjusted) ---
LOCAL_GUIDE_PATH = "../../data/guide_en.txt"
FAISS_INDEX_PATH = "../../faiss_guide_index"

class TextToCADAgent:
    def __init__(self, default_llm, advanced_llm, expert_llm, local_retriever=None):
        self.default_llm = default_llm
        self.advanced_llm = advanced_llm
        self.expert_llm = expert_llm
        self.local_retriever = local_retriever

        set_classification_llm(self.default_llm)

        self.MAX_QUESTION_ATTEMPTS = 5

        self._session_states = {}

        self.unified_processing_chain = create_unified_processing_chain(self.expert_llm, self.local_retriever)
        self.rag_code_generation_chain = create_code_generation_chain(self.advanced_llm)
        self.code_editing_chain = create_code_editing_chain(self.expert_llm)

    def _get_session_state(self, session_id):
        """Get the state for a specific session."""
        if session_id not in self._session_states:
            self._session_states[session_id] = {
                'latest_code': None,
                'latest_title': None,
                'latest_requirements': None,
                'user_text_history': [],
                'conversation_state': 'initial',
                'pending_questions': [],
                'previous_responses': [],
                'initiating_query_for_collection': None,
                'edit_request_history': [],
                'edit_context': {}
            }
        return self._session_states[session_id]

    def _update_session_state(self, session_id, **updates):
        session_state = self._get_session_state(session_id)
        session_state.update(updates)
        return session_state

    def reset_conversation(self, session_id: Optional[str] = None):
        if session_id:
            if session_id in self._session_states:
                print(f"[PROCESS] Resetting conversation state for session {session_id}")
                self._session_states[session_id] = {
                    'latest_code': None,
                    'latest_title': None,
                    'latest_requirements': None,
                    'user_text_history': [],
                    'conversation_state': 'initial',
                    'pending_questions': [],
                    'previous_responses': [],
                    'initiating_query_for_collection': None,
                    'edit_request_history': [],
                    'edit_context': {}
                }
                print(f"[SUCCESS] Conversation reset complete for session {session_id}")
            else:
                print(f"[WARNING] Attempted to reset non-existent session: {session_id}")
        else:
            print("[PROCESS] Resetting all session states.")
            self._session_states.clear()
            print("[SUCCESS] All session states reset complete.")

    def format_previous_responses_for_session(self, previous_responses_list: List[str]) -> str:
        if not previous_responses_list:
            return "No previous responses for this session."

        formatted = "Previous responses for this session:\n"
        for i, response in enumerate(previous_responses_list):
            formatted += f"{i+1}. {response}\n"
        return formatted

    def process_request(self, user_text, is_edit_request=False, request_origin='unknown', session_id=None):
        if session_id is None:
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        print(f"[DEBUG] Analyzing request: '{user_text}' for session {session_id}...")

        state = self._get_session_state(session_id)

        print(f"🔍 [SESSION STATE] Session: {session_id}")
        print(f"   - Is Edit Request: {is_edit_request}")
        print(f"   - Has Latest Code: {bool(state.get('latest_code'))}")
        print(f"   - Conversation State: {state.get('conversation_state', 'unknown')}")
        print(f"   - Previous Responses: {len(state.get('previous_responses', []))}")
        if state.get('latest_code'):
            print(f"   - Latest Code Length: {len(state['latest_code'])} characters")
        print(f"   - User Text History: {state.get('user_text_history', [])}")
        print()

        state['user_text_history'].append(user_text)

        skip_questions_keywords = ["dont ask anymore", "no more questions", "stop asking",
                                  "proceed anyway", "just continue", "skip questions"]
        skip_questions = any(keyword in user_text.lower() for keyword in skip_questions_keywords)

        if skip_questions:
            print(f"[WARNING] User requested to skip further questions. Session: {session_id}")
            if state['conversation_state'] == "collecting_info":
                return self.continue_information_collection(" ".join(state['user_text_history']), request_origin, session_id=session_id, force_proceed=True)
            elif state['conversation_state'] == "collecting_info_edit":
                 return self.continue_edit_information_collection(" ".join(state['user_text_history']), request_origin, session_id=session_id, force_proceed=True)

        if state['conversation_state'] in ["collecting_info", "collecting_info_edit"] and not skip_questions:
            state['previous_responses'].append(user_text)
            self._update_session_state(session_id, previous_responses=state['previous_responses'])
            print(f"[MESSAGE] Added user response to tracking: '{user_text}' for session {session_id}")
            combined_text = " ".join(state['user_text_history'])
            if is_edit_request and state['conversation_state'] == "collecting_info_edit":
                return self.continue_edit_information_collection(combined_text, request_origin, session_id=session_id)
            elif not is_edit_request and state['conversation_state'] == "collecting_info":
                return self.continue_information_collection(combined_text, request_origin, session_id=session_id)

        if is_edit_request and state['latest_code']:
            return self.process_edit_request(user_text, request_origin, session_id=session_id)
        else:
            return self.process_new_request(user_text, request_origin, session_id=session_id, skip_questions_requested=skip_questions)

    def process_new_request(self, user_text, request_origin='unknown', session_id=None, skip_questions_requested=False):
        if session_id is None:
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        state = self._get_session_state(session_id)

        try:
            chain_input = {
                "user_text": user_text,
                "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                "latest_requirements_for_guidance": state['latest_requirements']
            }

            print(f"🔍 [OPENAI INPUT - NEW REQUEST] Session: {session_id}")
            print(f"   - User Text: {user_text}")
            print(f"   - Previous Responses: {chain_input['previous_responses_formatted']}")
            print(f"   - Latest Requirements: {chain_input['latest_requirements_for_guidance']}")
            print(f"   - Skip Questions Requested: {skip_questions_requested}")
            print()

            unified_chain_result = self.unified_processing_chain.invoke(chain_input)

            unified_output_obj = unified_chain_result["unified_output_obj"]
            raw_unified_json = unified_chain_result["raw_unified_json"]
            retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]

            print(f"\n[SUCCESS] Unified analysis and parameter check successful for session {session_id}:")
            print(f"Parsed Output: {json.dumps(unified_output_obj.dict(), indent=2)}")

            self._update_session_state(session_id, latest_requirements=unified_output_obj)

            if unified_output_obj.title == "Unable to parse requirements":
                print(f"[ERROR] Parsing error in process_new_request for session {session_id}: {unified_output_obj.explanation}")
                return {
                    "code": None, "gltf_path": None,
                    "message": f"I had trouble understanding your request. Could you please try rephrasing it? Details: {unified_output_obj.explanation}",
                    "explanation": unified_output_obj.explanation
                }

            if unified_output_obj.missing_info and unified_output_obj.questions and not skip_questions_requested:
                print(f"\n❓ Missing information detected for session {session_id}. Questions: {unified_output_obj.questions}")

                truly_pending_questions = []
                if state['previous_responses']:
                    for q_text in unified_output_obj.questions:
                        q_keywords = set(q_text.lower().replace('?', '').split())
                        answered = False
                        for prev_resp in state['previous_responses']:
                            resp_keywords = set(prev_resp.lower().split())
                            if len(q_keywords.intersection(resp_keywords)) > 1:
                                answered = True
                                break
                        if not answered:
                            truly_pending_questions.append(q_text)
                else:
                    truly_pending_questions = unified_output_obj.questions

                if not truly_pending_questions and not skip_questions_requested:
                     print(f"[SUCCESS] All questions appear to have been answered in previous responses or user skipped for session {session_id}.")
                     return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id)

                if truly_pending_questions and not skip_questions_requested:
                    self._update_session_state(
                        session_id,
                        conversation_state="collecting_info",
                        pending_questions=truly_pending_questions,
                        initiating_query_for_collection=user_text
                    )

                    all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(truly_pending_questions)])
                    message_to_user = f"{all_questions_str}"
                    if unified_output_obj.explanation:
                        message_to_user += f"\n\n{unified_output_obj.explanation}"

                    return {
                        "code": None, "gltf_path": None,
                        "message": message_to_user, "explanation": unified_output_obj.explanation
                    }

            return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id)

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during unified new request processing for session {session_id}: {e}")
            print(f"Traceback: {error_traceback}")
            return {"error": f"Error analyzing requirements: {str(e)}", "code": None, "gltf_path": None}

    def continue_information_collection(self, combined_text, request_origin='unknown', session_id=None, force_proceed=False):
        if session_id is None:
            print("ERROR: session_id is None in continue_information_collection. Generating a new one.")
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        state = self._get_session_state(session_id)

        try:
            current_user_text_for_chain = combined_text

            chain_input = {
                "user_text": current_user_text_for_chain,
                "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                "latest_requirements_for_guidance": state['latest_requirements']
            }
            unified_chain_result = self.unified_processing_chain.invoke(chain_input)

            unified_output_obj = unified_chain_result["unified_output_obj"]
            raw_unified_json = unified_chain_result["raw_unified_json"]
            retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]

            print(f"\n[SUCCESS] Unified analysis and parameter check successful for session {session_id}:")
            print(f"Parsed Output: {json.dumps(unified_output_obj.dict(), indent=2)}")
            self._update_session_state(session_id, latest_requirements=unified_output_obj)

            if unified_output_obj.title == "Unable to parse requirements":
                print(f"[ERROR] Parsing error in continue_information_collection for session {session_id}: {unified_output_obj.explanation}")
                self._update_session_state(session_id, conversation_state="initial")
                return {
                    "code": None, "gltf_path": None,
                    "message": f"I had trouble understanding your latest information. Could you please try rephrasing it or provide different details? Error: {unified_output_obj.explanation}",
                    "explanation": unified_output_obj.explanation
                }

            if unified_output_obj.missing_info and unified_output_obj.questions and \
               len(state['previous_responses']) < self.MAX_QUESTION_ATTEMPTS and not force_proceed:

                self._update_session_state(session_id, pending_questions=unified_output_obj.questions)

                print(f"\n❓ Still missing information for session {session_id}. Asking questions: {state['pending_questions']}")

                all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(state['pending_questions'])])
                message_to_user = f"{all_questions_str}"
                if unified_output_obj.explanation:
                    message_to_user += f"\n\n{unified_output_obj.explanation}"

                return {
                    "code": None, "gltf_path": None,
                    "message": message_to_user, "explanation": unified_output_obj.explanation
                }

            print(f"\n[WARNING] Proceeding with available information to generate code for session {session_id}.")
            self._update_session_state(session_id, conversation_state="generating_code")
            return self.generate_code_from_requirements(unified_output_obj, raw_unified_json, retrieved_context_for_code_gen, request_origin, session_id=session_id)

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during continue_information_collection for session {session_id}: {e}")
            print(f"Traceback: {error_traceback}")
            return {"error": f"Error processing your information: {str(e)}", "code": None, "gltf_path": None}

    async def _generate_code_only(self, design_requirements_obj: AnalysisAndParameterCheckOutput, raw_design_requirements_json: str, retrieved_context: str, request_origin='unknown', session_id=None):
        """Generate FreeCAD code only, without file operations (for progress streaming)."""
        if session_id is None:
            print("ERROR: session_id is None in _generate_code_only. Cannot proceed.")
            return {"error": "Session ID missing in _generate_code_only", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)
        print(f"\nGenerating FreeCAD code for session {session_id}...")
        try:
            generated_code = self.rag_code_generation_chain.invoke({
                "design_requirements_obj": design_requirements_obj,
                "raw_design_requirements_json": raw_design_requirements_json,
                "retrieved_context": retrieved_context
            })
            print(f"[SUCCESS] FreeCAD code generation successful for session {session_id}.")

            self._update_session_state(
                session_id,
                latest_code=generated_code,
                latest_title=design_requirements_obj.title,
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None,
                user_text_history=[]
            )

            return {
                "code": generated_code,
                "message": "Code generated successfully."
            }

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during code generation for session {session_id}: {e}\n{error_traceback}")
            return {"error": f"Error generating code: {e}", "code": None, "gltf_path": None}

    def generate_code_from_requirements(self, design_requirements_obj: AnalysisAndParameterCheckOutput, raw_design_requirements_json: str, retrieved_context: str, request_origin='unknown', session_id=None):
        if session_id is None:
            print("ERROR: session_id is None in generate_code_from_requirements. Cannot proceed.")
            return {"error": "Session ID missing in generate_code_from_requirements", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)
        print(f"\nGenerating FreeCAD code for session {session_id}...")
        try:
            generated_code = self.rag_code_generation_chain.invoke({
                "design_requirements_obj": design_requirements_obj,
                "raw_design_requirements_json": raw_design_requirements_json,
                "retrieved_context": retrieved_context
            })
            print(f"[SUCCESS] FreeCAD code generation successful for session {session_id}.")

            self._update_session_state(
                session_id,
                latest_code=generated_code,
                latest_title=design_requirements_obj.title,
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None,
                user_text_history=[]
            )

            gltf_path, obj_path, step_path = self.save_outputs(generated_code, design_requirements_obj, request_origin=request_origin)
            return {
                "code": generated_code,
                "gltf_path": gltf_path,
                "obj_path": obj_path,
                "step_path": step_path,
                "message": "Code generated successfully."
            }

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"[ERROR] Error during code generation for session {session_id}: {e}\n{error_traceback}")
            return {"error": f"Error generating code: {e}", "code": None, "gltf_path": None}

    def process_edit_request(self, user_text, request_origin='unknown', session_id=None):
        if session_id is None:
            print("ERROR: session_id is None in process_edit_request. Cannot proceed.")
            return {"error": "Session ID missing in process_edit_request", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)

        if not state['latest_code']:
            print(f"[WARNING] No latest_code in agent session state for session {session_id}.")
            print(f"[ERROR] No existing code to edit for session {session_id}. Please generate code first.")
            return {"error": "No existing code to edit. Please generate code first.", "code": None, "gltf_path": None}

        print(f"\n[DEBUG] Processing edit request for session {session_id}: '{user_text}'...")

        if state['conversation_state'] == "collecting_info_edit" and state['pending_questions']:
            combined_text = " ".join(state['user_text_history'])
            return self.continue_edit_information_collection(combined_text, request_origin, session_id=session_id)

        state['edit_request_history'].append(user_text)

        try:
            if self.local_retriever:
                retrieved_docs = self.local_retriever.invoke(user_text)
                retrieved_context = format_retrieved_context(retrieved_docs)
            else:
                retrieved_context = "No local retriever available."

            print("[SUCCESS] Context retrieval successful.")

            state['edit_context']['retrieved_context'] = retrieved_context
            print(f"[DEBUG] Stored RAG context in edit_context for session {session_id}")

        except Exception as e:
            print(f"[ERROR] Error during context retrieval: {e}")
            retrieved_context = "Error retrieving context."
            state['edit_context']['retrieved_context'] = retrieved_context

        try:
            print(f"\nEditing FreeCAD code for session {session_id}...")

            sanitized_title = ""
            if state['latest_title']:
                sanitized_title = re.sub(r'[^\w\s-]', '', state['latest_title']).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')

            if not sanitized_title and state['latest_requirements'] and state['latest_requirements'].title:
                sanitized_title = re.sub(r'[^\w\s-]', '', state['latest_requirements'].title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')

            if not sanitized_title:
                sanitized_title = "edited_model"

            print(f"Using sanitized_title: '{sanitized_title}' for code editing in session {session_id}")

            edited_code = self.code_editing_chain.invoke({
                "original_code": state['latest_code'],
                "user_request": user_text,
                "retrieved_context": retrieved_context,
                "sanitized_title": sanitized_title
            })
            print(f"[SUCCESS] FreeCAD code editing successful for session {session_id}.")

            self._update_session_state(
                session_id,
                latest_code=edited_code,
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None
            )

        except Exception as e:
            print(f"[ERROR] Error during code editing for session {session_id}: {e}")
            return {"error": f"Error editing code: {e}", "code": None, "gltf_path": None}

        gltf_path, obj_path = None, None
        current_requirements = state['latest_requirements'] if state['latest_requirements'] else DesignRequirements(
            title=state['latest_title'] or "Edited_Model",
            shapes=[ShapeRequirement(shape_type="unknown", dimensions={"unknown": 0.0})],
            complexity_level=1
        )

        gltf_path, obj_path, step_path = self.save_outputs(edited_code, current_requirements, base_filename=sanitized_title, request_origin=request_origin)

        print(f"[SUCCESS] Code updated successfully for session {session_id}.")

        self._update_session_state(session_id, user_text_history=[])

        return {"code": edited_code, "gltf_path": gltf_path, "obj_path": obj_path, "step_path": step_path}

    def continue_edit_information_collection(self, combined_text, request_origin='unknown', session_id=None):
        if session_id is None:
            print("ERROR: session_id is None in continue_edit_information_collection. Cannot proceed.")
            return {"error": "Session ID missing", "code": None, "gltf_path": None}

        state = self._get_session_state(session_id)

        try:
            print(f"\n[WARNING] Proceeding with available information for edit in session {session_id}, even if incomplete")
            self._update_session_state(session_id, conversation_state="initial")

            retrieved_context = state.get('edit_context', {}).get('retrieved_context')

            if not retrieved_context or retrieved_context == "No context available.":
                print(f"[DEBUG] No stored RAG context found for session {session_id}, retrieving again...")
                try:
                    if self.local_retriever:
                        retrieved_docs = self.local_retriever.invoke(combined_text)
                        retrieved_context = format_retrieved_context(retrieved_docs)
                        state['edit_context']['retrieved_context'] = retrieved_context
                    else:
                        retrieved_context = "No local retriever available."
                        state['edit_context']['retrieved_context'] = retrieved_context
                except Exception as e:
                    print(f"[ERROR] Error during context retrieval in continue_edit_information_collection: {e}")
                    retrieved_context = "Error retrieving context."
                    state['edit_context']['retrieved_context'] = retrieved_context
            else:
                print(f"[DEBUG] Using stored RAG context for session {session_id}")

            print(f"\nEditing FreeCAD code with available information for session {session_id}...")

            sanitized_title = ""
            if state['latest_title']:
                sanitized_title = re.sub(r'[^\\w\\s-]', '', state['latest_title']).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')
            if not sanitized_title and state['latest_requirements'] and state['latest_requirements'].title:
                sanitized_title = re.sub(r'[^\\w\\s-]', '', state['latest_requirements'].title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128).replace(' ', '_')
            if not sanitized_title:
                sanitized_title = "edited_model"

            print(f"🔍 [OPENAI INPUT - EDIT MODE] Session: {session_id}")
            print(f"   - User Request: {combined_text}")
            print(f"   - Original Code Length: {len(state['latest_code'])} characters")
            print(f"   - Original Code Preview: {state['latest_code'][:200]}...")
            print(f"   - Retrieved Context: {retrieved_context[:200]}...")
            print(f"   - Sanitized Title: {sanitized_title}")
            print()

            edited_code = self.code_editing_chain.invoke({
                "original_code": state['latest_code'],
                "user_request": combined_text,
                "retrieved_context": retrieved_context,
                "sanitized_title": sanitized_title
            })
            print(f"[SUCCESS] FreeCAD code editing successful for session {session_id}.")

            current_requirements = state['latest_requirements'] if state['latest_requirements'] else DesignRequirements(
                title=state['latest_title'] or "Edited_Model",
                shapes=[ShapeRequirement(shape_type="unknown", dimensions={"unknown": 0.0})],
                complexity_level=1
            )
            gltf_path, obj_path, step_path = self.save_outputs(edited_code, current_requirements, base_filename=sanitized_title, request_origin=request_origin)

            self._update_session_state(
                session_id,
                latest_code=edited_code,
                conversation_state="initial",
                pending_questions=[],
                previous_responses=[],
                initiating_query_for_collection=None,
                user_text_history=[],
                edit_context={}
            )

            return {"code": edited_code, "gltf_path": gltf_path, "obj_path": obj_path, "step_path": step_path}

        except Exception as e:
            print(f"[ERROR] Error during edit information collection: {e}")
            return {"error": f"Error processing your information for the edit: {e}", "code": None, "gltf_path": None}

    def save_outputs(self, code, design_requirements, base_filename="generated_cad", request_origin='unknown'):
        from src.utils.file_manager import save_code_file, save_metadata_file, execute_freecad_script, convert_obj_to_gltf
        from src.utils.path_manager import get_output_path, get_unique_filepath

        gltf_file_path = None

        shape_type = "unknown"
        dimensions = {}

        if hasattr(design_requirements, 'shapes') and design_requirements.shapes:
            primary_shape = design_requirements.shapes[0]
            shape_type = primary_shape.shape_type

            if hasattr(primary_shape, 'dimensions') and primary_shape.dimensions:
                dimensions = primary_shape.dimensions

        if not dimensions:
            dimensions = design_requirements.title or base_filename

        if shape_type == "unknown" and design_requirements.title:
            shape_type = design_requirements.title

        try:
            code_filepath = save_code_file(code, shape_type, dimensions, design_requirements.dict())
            print(f"💾 Saved generated code to: {code_filepath}")
        except Exception as e:
            print(f"[ERROR] Error saving code file: {e}")
            code_filepath = None

        try:
            json_filepath = save_metadata_file(design_requirements.dict(), shape_type, dimensions, design_requirements.dict())
            print(f"💾 Saved design requirements to: {json_filepath}")
        except Exception as e:
            print(f"[ERROR] Error saving metadata file: {e}")
            json_filepath = None

        if code_filepath:
            try:
                success, output = execute_freecad_script(code_filepath)
                if success:
                    print(f"🚀 Successfully executed FreeCAD script: {code_filepath}")
                else:
                    print(f"[WARNING] Warning: FreeCAD script execution may have issues: {output}")
            except Exception as e:
                print(f"[ERROR] Error executing FreeCAD script: {e}")

        from src.utils.path_manager import OBJ_OUTPUT_DIR, GLTF_OUTPUT_DIR, CAD_OUTPUT_DIR, PROJECT_ROOT
        import datetime

        today = datetime.datetime.now().strftime("%Y-%m-%d")
        obj_dir = OBJ_OUTPUT_DIR / today
        gltf_dir = GLTF_OUTPUT_DIR / today
        step_dir = CAD_OUTPUT_DIR / today

        obj_dir.mkdir(parents=True, exist_ok=True)
        gltf_dir.mkdir(parents=True, exist_ok=True)
        step_dir.mkdir(parents=True, exist_ok=True)

        base_filename = f"{shape_type}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

        obj_path = obj_dir / f"{base_filename}.obj"
        step_path = step_dir / f"{base_filename}.step"

        import glob
        import time
        from pathlib import Path

        recent_time = time.time() - 10
        obj_files = []

        for obj_file in glob.glob(str(PROJECT_ROOT / "**/*.obj"), recursive=True):
            if os.path.getmtime(obj_file) > recent_time:
                obj_files.append(obj_file)

        parent_dir = PROJECT_ROOT.parent
        for obj_file in glob.glob(str(parent_dir / "**/*.obj"), recursive=True):
            if os.path.getmtime(obj_file) > recent_time:
                obj_files.append(obj_file)

        if obj_files:
            obj_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
            found_obj_path = obj_files[0]

            print(f"[DEBUG] Found recently created OBJ file: {found_obj_path}")

            try:
                import shutil
                shutil.copy2(found_obj_path, obj_path)
                print(f"📋 Copied to organized location: {obj_path}")
            except Exception as e:
                print(f"[WARNING] Warning: Could not copy OBJ file: {e}")
                obj_path = Path(found_obj_path)

            if obj_path.exists():
                try:
                    success, gltf_result_path = convert_obj_to_gltf(obj_path)
                    if success and gltf_result_path:
                        print(f"[SUCCESS] Successfully converted OBJ to GLTF: {gltf_result_path}")
                        gltf_file_path = str(gltf_result_path)
                    else:
                        print(f"[WARNING] Warning: OBJ to GLTF conversion failed")
                except Exception as e:
                    print(f"[ERROR] Error during OBJ to GLTF conversion: {e}")
        else:
            print(f"[WARNING] Warning: No recently created OBJ files found")

        step_files = []

        recent_time = time.time() - 30

        cad_outputs_dir = PROJECT_ROOT / "outputs" / "code" / "cad_outputs_generated"
        print(f"[DEBUG] Specifically checking for STEP files in: {cad_outputs_dir}")
        if cad_outputs_dir.exists():
            for step_file in glob.glob(str(cad_outputs_dir / "*.step")):
                step_files.append(step_file)
                print(f"[DEBUG] Found STEP file in cad_outputs_generated: {step_file}, modified: {os.path.getmtime(step_file)}, recent threshold: {recent_time}")

        if not step_files:
            print("[DEBUG] No STEP files found in cad_outputs_generated, checking project directory...")
            for step_file in glob.glob(str(PROJECT_ROOT / "**/*.step"), recursive=True):
                if os.path.getmtime(step_file) > recent_time:
                    step_files.append(step_file)
                    print(f"[DEBUG] Found STEP file in project directory: {step_file}")

            print("[DEBUG] Checking parent directory for STEP files...")
            for step_file in glob.glob(str(PROJECT_ROOT.parent / "**/*.step"), recursive=True):
                if os.path.getmtime(step_file) > recent_time:
                    step_files.append(step_file)
                    print(f"[DEBUG] Found STEP file in parent directory: {step_file}")

        if step_files:
            step_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
            found_step_path = step_files[0]

            print(f"[DEBUG] Found recently created STEP file: {found_step_path}")

            try:
                shutil.copy2(found_step_path, step_path)
                print(f"📋 Copied to organized location: {step_path}")

                if request_origin == 'web':
                    print(f"🚀 Launching STEP viewer...")
                    try:
                        gui_script_path = os.path.abspath("gui_step.py")

                        if not os.path.exists(gui_script_path):
                            gui_script_path = os.path.abspath(os.path.join("Tolery", "gui_step.py"))
                            print(f"Using direct path to STEP viewer: {gui_script_path}")

                        subprocess.Popen([sys.executable, gui_script_path, str(step_path)])
                        print("[SUCCESS] STEP viewer launched.")
                    except Exception as e:
                        print(f"[ERROR] Error launching STEP viewer: {e}")
                else:
                    print("[INFO] Skipping STEP viewer launch for non-web request.")
            except Exception as e:
                print(f"[WARNING] Warning: Could not copy STEP file: {e}")
                step_path = Path(found_step_path)
        else:
            print(f"[WARNING] Warning: No recently created STEP files found")

            if obj_path and obj_path.exists():
                potential_step_path = Path(str(obj_path).replace('.obj', '.step'))
                if potential_step_path.exists():
                    print(f"[DEBUG] Fallback: Found STEP file with matching name: {potential_step_path}")
                    try:
                        shutil.copy2(potential_step_path, step_path)
                        print(f"📋 Copied to organized location: {step_path}")
                    except Exception as e:
                        print(f"[WARNING] Warning: Could not copy fallback STEP file: {e}")
                        step_path = potential_step_path

            if design_requirements and design_requirements.title:
                sanitized_title = re.sub(r'[^\w\s-]', '', design_requirements.title).strip()
                sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128)
                sanitized_title = sanitized_title.replace(' ', '_')

                if sanitized_title:
                    for potential_dir in [cad_outputs_dir, PROJECT_ROOT / "outputs" / "code"]:
                        if potential_dir.exists():
                            for step_file in glob.glob(str(potential_dir / f"{sanitized_title}*.step")):
                                print(f"[DEBUG] Fallback 2: Found STEP file matching title: {step_file}")
                                try:
                                    shutil.copy2(step_file, step_path)
                                    print(f"📋 Copied to organized location: {step_path}")
                                    break
                                except Exception as e:
                                    print(f"[WARNING] Warning: Could not copy title-matched STEP file: {e}")
                                    step_path = Path(step_file)

            if cad_outputs_dir.exists():
                all_step_files = list(cad_outputs_dir.glob("*.step"))
                if all_step_files:
                    newest_step = max(all_step_files, key=lambda p: p.stat().st_mtime)
                    print(f"[DEBUG] Fallback 3: Using newest STEP file in directory: {newest_step}")
                    try:
                        shutil.copy2(newest_step, step_path)
                        print(f"📋 Copied to organized location: {step_path}")
                    except Exception as e:
                        print(f"[WARNING] Warning: Could not copy newest STEP file: {e}")
                        step_path = newest_step

        print("\n📁 Generated Files Summary:")
        if code_filepath:
            print(f"  - Python Code: {code_filepath}")
        if json_filepath:
            print(f"  - JSON Requirements: {json_filepath}")
        if step_path and step_path.exists():
            print(f"  - STEP Model: {step_path}")
        if gltf_file_path:
            print(f"  - GLTF Model: {gltf_file_path}")
        if obj_path and obj_path.exists():
            print(f"  - OBJ Model: {obj_path}")
        print("")

        obj_path_to_return = str(obj_path) if obj_path and obj_path.exists() else None
        step_path_to_return = str(step_path) if step_path and step_path.exists() else None
        return gltf_file_path, obj_path_to_return, step_path_to_return

    async def process_request_with_progress(self, user_text, is_edit_request=False, request_origin='unknown', session_id=None):
        import asyncio
        import logging
        import time
        import traceback
        
        logger = logging.getLogger(__name__)
        start_time = time.time()
        
        if session_id is None:
            import uuid
            import random
            rand_digits = random.randint(100000, 999999)
            rand_uuid = uuid.uuid4().hex[:6]
            session_id = f"session_{rand_uuid}_{rand_digits}"

        logger.info(f"[AGENT_PROGRESS] Starting process_request_with_progress: '{user_text[:50]}...' for session {session_id}")
        logger.info(f"[AGENT_PROGRESS] Parameters - edit: {is_edit_request}, origin: {request_origin}")
        print(f"[DEBUG] Starting process_request_with_progress: '{user_text}' for session {session_id}...")

        try:
            logger.info(f"[AGENT_STEP] Starting analysis step for session {session_id}")
            yield {
                "step": "analysis",
                "status": "Analyzing user requirements...",
                "is_complete": False,
                "is_active": True,
                "progress": 10
            }
            
            logger.info(f"[AGENT_STATE] Getting session state for session {session_id}")
            state = self._get_session_state(session_id)
            logger.info(f"[AGENT_STATE] Current conversation state: {state['conversation_state']}")
            logger.info(f"[AGENT_STATE] User text history count: {len(state['user_text_history'])}")
            logger.info(f"[AGENT_STATE] Previous responses count: {len(state['previous_responses'])}")
            
            state['user_text_history'].append(user_text)
            logger.info(f"[AGENT_STATE] Added user text to history, new count: {len(state['user_text_history'])}")
            
            skip_questions_keywords = ["dont ask anymore", "no more questions", "stop asking",
                                      "proceed anyway", "just continue", "skip questions"]
            skip_questions = any(keyword in user_text.lower() for keyword in skip_questions_keywords)
            logger.info(f"[AGENT_STATE] Skip questions detected: {skip_questions}")

            if state['conversation_state'] in ["collecting_info", "collecting_info_edit"] and not skip_questions:
                logger.info(f"[AGENT_CONTINUATION] Processing continuation scenario - state: {state['conversation_state']}")
                state['previous_responses'].append(user_text)
                self._update_session_state(session_id, previous_responses=state['previous_responses'])
                print(f"[MESSAGE] Added user response to tracking: '{user_text}' for session {session_id}")
                logger.info(f"[AGENT_CONTINUATION] Added user response, total responses: {len(state['previous_responses'])}")
                combined_text = " ".join(state['user_text_history'])
                logger.info(f"[AGENT_CONTINUATION] Combined text length: {len(combined_text)} characters")
                
                yield {
                    "step": "analysis",
                    "status": "Analysis completed - continuing from previous session.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 20
                }
                
                logger.info(f"[AGENT_STEP] Starting parameters re-analysis step for session {session_id}")
                yield {
                    "step": "parameters",
                    "status": "Re-analyzing with additional information...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 30
                }
                
                await asyncio.sleep(0.5)
                
                if is_edit_request and state['conversation_state'] == "collecting_info_edit":
                    logger.info(f"[AGENT_EDIT] Processing edit continuation for session {session_id}")
                    yield {
                        "step": "parameters",
                        "status": "Parameters complete - proceeding with edit.",
                        "is_complete": True,
                        "is_active": False,
                        "progress": 40
                    }
                    
                    logger.info(f"[AGENT_STEP] Starting code editing step for session {session_id}")
                    yield {
                        "step": "generation_code",
                        "status": "Editing FreeCAD Python code...",
                        "is_complete": False,
                        "is_active": True,
                        "progress": 50
                    }
                    
                    await asyncio.sleep(0.5)
                    
                    logger.info(f"[AGENT_EDIT] Calling continue_edit_information_collection for session {session_id}")
                    edit_start_time = time.time()
                    result = self.continue_edit_information_collection(combined_text, request_origin, session_id=session_id)
                    edit_duration = time.time() - edit_start_time
                    logger.info(f"[AGENT_EDIT] Edit processing completed in {edit_duration:.2f}s for session {session_id}")
                    
                    yield {
                        "step": "generation_code",
                        "status": "Code editing completed.",
                        "is_complete": True,
                        "is_active": False,
                        "progress": 70
                    }
                    
                elif not is_edit_request and state['conversation_state'] == "collecting_info":
                    logger.info(f"[AGENT_INFO] Processing information collection continuation for session {session_id}")
                    try:
                        logger.info(f"[AGENT_CHAIN] Preparing unified analysis chain input for session {session_id}")
                        chain_input = {
                            "user_text": combined_text,
                            "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                            "latest_requirements_for_guidance": state['latest_requirements']
                        }
                        
                        logger.info(f"[AGENT_CHAIN] Invoking unified processing chain for session {session_id}")
                        chain_start_time = time.time()
                        unified_chain_result = self.unified_processing_chain.invoke(chain_input)
                        chain_duration = time.time() - chain_start_time
                        logger.info(f"[AGENT_CHAIN] Chain processing completed in {chain_duration:.2f}s for session {session_id}")
                        
                        unified_output_obj = unified_chain_result["unified_output_obj"]
                        raw_unified_json = unified_chain_result["raw_unified_json"]
                        retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]
                        
                        print(f"\n[SUCCESS] Unified analysis and parameter check successful for session {session_id}:")
                        print(f"Parsed Output: {json.dumps(unified_output_obj.dict(), indent=2)}")
                        
                        logger.info(f"[AGENT_CHAIN] Missing info: {unified_output_obj.missing_info}")
                        logger.info(f"[AGENT_CHAIN] Questions count: {len(unified_output_obj.questions) if unified_output_obj.questions else 0}")
                        
                        self._update_session_state(session_id, latest_requirements=unified_output_obj)
                        
                        if unified_output_obj.missing_info and unified_output_obj.questions and \
                           len(state['previous_responses']) < self.MAX_QUESTION_ATTEMPTS and not skip_questions:
                            
                            logger.info(f"[AGENT_QUESTIONS] Still missing info, asking more questions for session {session_id}")
                            logger.info(f"[AGENT_QUESTIONS] Response attempt {len(state['previous_responses'])}/{self.MAX_QUESTION_ATTEMPTS}")
                            
                            yield {
                                "step": "parameters",
                                "status": "Additional parameters still needed...",
                                "is_complete": False,
                                "is_active": True,
                                "progress": 35
                            }
                            
                            self._update_session_state(session_id, pending_questions=unified_output_obj.questions)
                            
                            all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(unified_output_obj.questions)])
                            message_to_user = f"{all_questions_str}"
                            if unified_output_obj.explanation:
                                message_to_user += f"\n\n{unified_output_obj.explanation}"
                            
                            result = {
                                "code": None, "gltf_path": None,
                                "message": message_to_user, "explanation": unified_output_obj.explanation
                            }
                            logger.info(f"[AGENT_QUESTIONS] Returning questions to user for session {session_id}")
                            yield {"final_result": result}
                            return
                        
                        logger.info(f"[AGENT_PARAMS] Parameters complete, proceeding to code generation for session {session_id}")
                        yield {
                            "step": "parameters",
                            "status": "Parameters complete - proceeding with code generation.",
                            "is_complete": True,
                            "is_active": False,
                            "progress": 40
                        }
                        
                        logger.info(f"[AGENT_STEP] Starting code generation step for session {session_id}")
                        yield {
                            "step": "generation_code",
                            "status": "Generating FreeCAD Python code...",
                            "is_complete": False,
                            "is_active": True,
                            "progress": 50
                        }
                        
                        await asyncio.sleep(0.5)
                        
                        logger.info(f"[AGENT_CODEGEN] Calling _generate_code_only for session {session_id}")
                        codegen_start_time = time.time()
                        result = await self._generate_code_only(
                            unified_output_obj, 
                            raw_unified_json, 
                            retrieved_context_for_code_gen, 
                            request_origin, 
                            session_id=session_id
                        )
                        codegen_duration = time.time() - codegen_start_time
                        logger.info(f"[AGENT_CODEGEN] Code generation completed in {codegen_duration:.2f}s for session {session_id}")
                        
                        yield {
                            "step": "generation_code",
                            "status": "Code generation completed.",
                            "is_complete": True,
                            "is_active": False,
                            "progress": 70
                        }
                        
                        if result.get("error"):
                            logger.error(f"[AGENT_CODEGEN] Code generation error for session {session_id}: {result.get('error')}")
                            yield {"final_result": result}
                            return
                        
                    except Exception as e:
                        logger.error(f"[AGENT_INFO] Error during continuation processing for session {session_id}: {str(e)}")
                        logger.error(f"[AGENT_INFO] Traceback: {traceback.format_exc()}")
                        result = {"error": f"Error processing continuation: {str(e)}", "code": None, "gltf_path": None}
                        yield {"final_result": result}
                        return
                else:
                    logger.error(f"[AGENT_CONTINUATION] Unexpected conversation state for session {session_id}: {state['conversation_state']}")
                    result = {"error": "Unexpected conversation state"}
                    yield {"final_result": result}
                    return

                yield {
                    "step": "export",
                    "status": "Exporting files and executing FreeCAD...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 80
                }
                
                await asyncio.sleep(0.3)
                
                if not is_edit_request and result.get("code"):
                    try:
                        generated_code = result.get("code")
                        gltf_path, obj_path, step_path = self.save_outputs(
                            generated_code, 
                            unified_output_obj, 
                            request_origin=request_origin
                        )
                        
                        result.update({
                            "gltf_path": gltf_path,
                            "obj_path": obj_path,
                            "step_path": step_path
                        })
                    except Exception as e:
                        print(f"[ERROR] Error during file export for session {session_id}: {e}")
                        result["error"] = f"Code generated but export failed: {str(e)}"
                
                yield {
                    "step": "export",
                    "status": "Export completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 95
                }
                
                logger.info(f"[AGENT_STEP] Starting completion step for session {session_id}")
                yield {
                    "step": "complete",
                    "status": "All processing completed!",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 100
                }
                
                total_duration = time.time() - start_time
                logger.info(f"[AGENT_PROGRESS] Process completed in {total_duration:.2f}s for session {session_id}")
                yield {"final_result": result}
                return

            if is_edit_request and state['latest_code']:
                yield {
                    "step": "analysis", 
                    "status": "Analysis completed - edit mode.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 20
                }
                
                yield {
                    "step": "parameters",
                    "status": "Parameters not needed for edit mode.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 40
                }
                
                logger.info(f"[AGENT_STEP] Starting code editing step for session {session_id}")
                yield {
                    "step": "generation_code",
                    "status": "Editing FreeCAD Python code...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 50
                }
                
                await asyncio.sleep(0.5)
                
                logger.info(f"[AGENT_EDIT] Calling process_edit_request for session {session_id}")
                edit_start_time = time.time()
                result = self.process_edit_request(user_text, request_origin, session_id=session_id)
                edit_duration = time.time() - edit_start_time
                logger.info(f"[AGENT_EDIT] Edit processing completed in {edit_duration:.2f}s for session {session_id}")
                
                yield {
                    "step": "generation_code",
                    "status": "Code editing completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 70
                }
                
                if result.get("error"):
                    logger.error(f"[AGENT_EDIT] Edit error for session {session_id}: {result.get('error')}")
                    yield {"final_result": result}
                    return
                
                logger.info(f"[AGENT_STEP] Starting export step for session {session_id}")
                yield {
                    "step": "export",
                    "status": "Exporting edited files...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 80
                }
                
                await asyncio.sleep(0.3)
                
                yield {
                    "step": "export",
                    "status": "Export completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 95
                }
                
                logger.info(f"[AGENT_STEP] Starting completion step for session {session_id}")
                yield {
                    "step": "complete",
                    "status": "All editing completed!",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 100
                }
                
                total_duration = time.time() - start_time
                logger.info(f"[AGENT_PROGRESS] Edit process completed in {total_duration:.2f}s for session {session_id}")
                yield {"final_result": result}
                return
            else:
                logger.info(f"[AGENT_NEW] Processing new request for session {session_id}")
                logger.info(f"[AGENT_CHAIN] Preparing unified analysis chain input for session {session_id}")
                chain_input = {
                    "user_text": user_text,
                    "previous_responses_formatted": self.format_previous_responses_for_session(state['previous_responses']),
                    "latest_requirements_for_guidance": state['latest_requirements']
                }
                
                await asyncio.sleep(0.5)
                
                logger.info(f"[AGENT_CHAIN] Invoking unified processing chain for session {session_id}")
                chain_start_time = time.time()
                unified_chain_result = self.unified_processing_chain.invoke(chain_input)
                chain_duration = time.time() - chain_start_time
                logger.info(f"[AGENT_CHAIN] Chain processing completed in {chain_duration:.2f}s for session {session_id}")
                
                unified_output_obj = unified_chain_result["unified_output_obj"]
                raw_unified_json = unified_chain_result["raw_unified_json"]
                retrieved_context_for_code_gen = unified_chain_result["retrieved_context_for_code_gen"]
                
                print(f"\n[SUCCESS] Unified analysis and parameter check successful for session {session_id}:")
                print(f"Parsed Output: {json.dumps(unified_output_obj.dict(), indent=2)}")
                
                self._update_session_state(session_id, latest_requirements=unified_output_obj)
                
                yield {
                    "step": "analysis",
                    "status": "Analysis completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 20
                }
                
                if unified_output_obj.title == "Unable to parse requirements":
                    logger.warning(f"[AGENT_PARSE] Unable to parse requirements for session {session_id}")
                    result = {
                        "code": None, "gltf_path": None,
                        "message": f"I had trouble understanding your request. Could you please try rephrasing it? Details: {unified_output_obj.explanation}",
                        "explanation": unified_output_obj.explanation
                    }
                    yield {"final_result": result}
                    return
                
                logger.info(f"[AGENT_CHAIN] Missing info: {unified_output_obj.missing_info}")
                logger.info(f"[AGENT_CHAIN] Questions count: {len(unified_output_obj.questions) if unified_output_obj.questions else 0}")
                
                if unified_output_obj.missing_info and unified_output_obj.questions and not skip_questions:
                    logger.info(f"[AGENT_QUESTIONS] Missing info detected, preparing questions for session {session_id}")
                    yield {
                        "step": "parameters",
                        "status": "Missing parameters detected, waiting for user input...",
                        "is_complete": False,
                        "is_active": True,
                        "progress": 30
                    }
                    
                    truly_pending_questions = []
                    if state['previous_responses']:
                        for q_text in unified_output_obj.questions:
                            q_keywords = set(q_text.lower().replace('?', '').split())
                            answered = False
                            for prev_resp in state['previous_responses']:
                                resp_keywords = set(prev_resp.lower().split())
                                if len(q_keywords.intersection(resp_keywords)) > 1:
                                    answered = True
                                    break
                            if not answered:
                                truly_pending_questions.append(q_text)
                    else:
                        truly_pending_questions = unified_output_obj.questions
                    
                    if truly_pending_questions:
                        logger.info(f"[AGENT_QUESTIONS] {len(truly_pending_questions)} pending questions for session {session_id}")
                        self._update_session_state(
                            session_id,
                            conversation_state="collecting_info",
                            pending_questions=truly_pending_questions,
                            initiating_query_for_collection=user_text
                        )
                        
                        yield {
                            "step": "parameters",
                            "status": "Parameter collection completed - waiting for user input.",
                            "is_complete": True,
                            "is_active": False,
                            "progress": 40
                        }
                        
                        all_questions_str = "\n".join([f"{i+1}. {q}" for i, q in enumerate(truly_pending_questions)])
                        message_to_user = f"{all_questions_str}"
                        if unified_output_obj.explanation:
                            message_to_user += f"\n\n{unified_output_obj.explanation}"
                        
                        result = {
                            "code": None, "gltf_path": None,
                            "message": message_to_user, "explanation": unified_output_obj.explanation
                        }
                        logger.info(f"[AGENT_QUESTIONS] Returning questions to user for session {session_id}")
                        yield {"final_result": result}
                        return
                
                logger.info(f"[AGENT_PARAMS] No missing info, proceeding to code generation for session {session_id}")
                yield {
                    "step": "parameters",
                    "status": "Parameters complete - no additional info needed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 40
                }
                
                logger.info(f"[AGENT_STEP] Starting code generation step for session {session_id}")
                yield {
                    "step": "generation_code",
                    "status": "Generating FreeCAD Python code...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 50
                }
                
                await asyncio.sleep(0.5)
                
                logger.info(f"[AGENT_CODEGEN] Calling _generate_code_only for session {session_id}")
                codegen_start_time = time.time()
                result = await self._generate_code_only(
                    unified_output_obj, 
                    raw_unified_json, 
                    retrieved_context_for_code_gen, 
                    request_origin, 
                    session_id=session_id
                )
                codegen_duration = time.time() - codegen_start_time
                logger.info(f"[AGENT_CODEGEN] Code generation completed in {codegen_duration:.2f}s for session {session_id}")
                
                yield {
                    "step": "generation_code",
                    "status": "Code generation completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 70
                }
                
                if result.get("error"):
                    logger.error(f"[AGENT_CODEGEN] Code generation error for session {session_id}: {result.get('error')}")
                    yield {"final_result": result}
                    return
                
                logger.info(f"[AGENT_STEP] Starting export step for session {session_id}")
                yield {
                    "step": "export",
                    "status": "Exporting files and executing FreeCAD...",
                    "is_complete": False,
                    "is_active": True,
                    "progress": 80
                }
                
                await asyncio.sleep(0.3)
                
                try:
                    logger.info(f"[AGENT_EXPORT] Starting file operations for session {session_id}")
                    export_start_time = time.time()
                    generated_code = result.get("code")
                    if generated_code:
                        gltf_path, obj_path, step_path = self.save_outputs(
                            generated_code, 
                            unified_output_obj, 
                            request_origin=request_origin
                        )
                        export_duration = time.time() - export_start_time
                        logger.info(f"[AGENT_EXPORT] File operations completed in {export_duration:.2f}s for session {session_id}")
                        
                        result.update({
                            "gltf_path": gltf_path,
                            "obj_path": obj_path,
                            "step_path": step_path
                        })
                except Exception as e:
                    logger.error(f"[AGENT_EXPORT] Error during file export for session {session_id}: {str(e)}")
                    result["error"] = f"Code generated but export failed: {str(e)}"
                
                yield {
                    "step": "export",
                    "status": "Export completed.",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 95
                }
                
                logger.info(f"[AGENT_STEP] Starting completion step for session {session_id}")
                yield {
                    "step": "complete",
                    "status": "All processing completed!",
                    "is_complete": True,
                    "is_active": False,
                    "progress": 100
                }
                
                total_duration = time.time() - start_time
                logger.info(f"[AGENT_PROGRESS] Process completed in {total_duration:.2f}s for session {session_id}")
                yield {"final_result": result}
                return
                
        except Exception as e:
            total_duration = time.time() - start_time
            logger.error(f"[AGENT_PROGRESS] Error during process_request_with_progress after {total_duration:.2f}s for session {session_id}: {str(e)}")
            logger.error(f"[AGENT_PROGRESS] Traceback: {traceback.format_exc()}")
            result = {"error": f"Error processing request: {str(e)}", "code": None, "gltf_path": None}
            yield {"final_result": result}
            return
