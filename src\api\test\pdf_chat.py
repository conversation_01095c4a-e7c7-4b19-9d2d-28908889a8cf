"""
PDF Chat Router for API-TEST
Handles PDF upload and processing with CAD generation.
"""

import os
import tempfile
import logging
from pathlib import Path
from typing import Optional

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

# Configure logging
logger = logging.getLogger(__name__)

# Database and model imports
try:
    from ...database.database import get_db
    from ...models.sessions import Session as SessionModel, ChatHistory
    from ...crud import chat_processing as crud
    from ...core.chatbot import text_to_cad_agent
    from .pdf_handler import PDFProcessor  # Use local PDF handler
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import get_db
    from src.models.sessions import Session as SessionModel, ChatHistory
    from src.crud import chat_processing as crud
    from src.core.chatbot import text_to_cad_agent
    # Try local first, then fallback to src.utils
    try:
        from .pdf_handler import PDFProcessor
    except ImportError:
        from src.utils.pdf_handler import PDFProcessor

# Initialize PDF processor
pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)

# Create router
router = APIRouter(
    prefix="/chat-pdf",
    tags=["pdf-chat"],
    responses={404: {"description": "Not found"}},
)

# Request/Response Models
class PDFChatRequest(BaseModel):
    """PDF chat request model."""
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

class PDFChatResponse(BaseModel):
    """PDF chat response model."""
    success: bool = Field(..., description="Whether the processing was successful")
    user_message: str = Field(..., description="User input message")
    response: str = Field(..., description="Toolchat response message")
    session_id: str = Field(..., description="Session ID")
    obj_export: Optional[str] = Field(None, description="OBJ file URL if generated")
    step_export: Optional[str] = Field(None, description="STEP file URL if generated")
    error: Optional[str] = Field(None, description="Error message if any")

@router.post("/", response_model=PDFChatResponse)
async def chat_pdf(
    file: UploadFile = File(..., description="PDF file to process"),
    session_id: Optional[str] = Form(None, description="Optional session ID - will generate if not provided"),
    user_input: str = Form("", description="Additional user input or instructions"),
    db: Session = Depends(get_db)
):
    """
    Process a PDF file and generate CAD code.

    Workflow:
    1. Upload PDF file
    2. Analyze PDF to extract class/name information
    3. Pass analysis results to chat tool
    4. Generate code/obj/step files like normal chat functionality
    """
    logger.info(f"PDF chat request received: {file.filename} (session_id: {session_id})")

    # Validate file is a PDF
    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        # Process the PDF using the PDFProcessor, passing the session_id
        success, analysis_result, final_session_id = await pdf_processor.process_uploaded_file(db, file, user_input, session_id)

        # Use the final session_id (either provided or generated)
        session_id = final_session_id

        if not success:
            logger.error(f"PDF processing failed: {analysis_result}")
            return PDFChatResponse(
                success=False,
                user_message=user_input,
                response=analysis_result,
                session_id=session_id or "unknown",
                error="PDF processing failed"
            )

        # Create message for chat tool based on PDF analysis
        chat_message = f"Based on PDF analysis: {analysis_result}. {user_input if user_input else ''}"

        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=chat_message,
            session_id=session_id,
            image_path="",
            part_file_name="part_file_name",
            export_format="obj",
            material_choice="STEEL",
            selected_feature_uuid="",
            is_edit_request=False
        )

        # Process using chat tool
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='pdf')

        # Override the user message in database to save only user input, not full analysis
        if hasattr(result, 'session_id') and result.session_id:
            # Update the latest chat history entry to have user_input as message instead of full analysis
            try:
                from ...models.sessions import ChatHistory
                latest_entry = db.query(ChatHistory).filter(
                    ChatHistory.session_id == result.session_id
                ).order_by(ChatHistory.id.desc()).first()

                if latest_entry:
                    latest_entry.message = user_input  # Override with user input only
                    db.commit()
                    logger.info(f"Updated chat history message to user input: {user_input}")
            except Exception as e:
                logger.warning(f"Failed to update chat history message: {e}")

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing PDF chat request: {result['error']}")
            return PDFChatResponse(
                success=False,
                user_message=user_input,
                response=result.get("message", "An error occurred"),
                session_id=session_id or "unknown",
                error=result["error"]
            )

        # Return successful response
        return PDFChatResponse(
            success=True,
            user_message=user_input,
            response=result.chat_response,
            session_id=result.session_id,
            obj_export=result.obj_export,
            step_export=result.step_export
        )

    except Exception as e:
        logger.error(f"Error processing PDF chat request: {e}")
        return PDFChatResponse(
            success=False,
            user_message=user_input,
            response="An error occurred while processing the PDF",
            session_id=session_id or "unknown",
            error=str(e)
        )
