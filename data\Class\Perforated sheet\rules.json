{"class_name": "Perforated sheet", "rules": [{"id": "PS_001", "category": "geometry_validation", "title": "Hole Collision Detection", "description": "Holes on the perforated sheet must not collide with each other", "rule": "Center-to-center distance between holes must be greater than the sum of their radii", "severity": "error", "validation_code": "center_distance > (radius1 + radius2)", "error_message": "WARNING: Holes are colliding. Increase spacing or reduce hole size.", "parameters": ["center_to_center_x", "center_to_center_y", "hole_diameter"]}, {"id": "PS_002", "category": "manufacturing_constraint", "title": "Minimum Edge Distance", "description": "Distance from hole to sheet edge must be sufficient", "rule": "Distance from hole center to sheet edge >= 1.5 * hole radius", "severity": "warning", "validation_code": "edge_distance >= 1.5 * hole_radius", "error_message": "WARNING: Hole too close to sheet edge, may cause deformation during processing.", "parameters": ["length", "width", "hole_diameter"]}]}