"""
Database connection and session management for Tolery API.
"""
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import os
from pathlib import Path

# Load environment variables from root directory
config_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(str(config_path))

# Get database connection parameters from environment variables
DB_USER = os.getenv("MYSQL_USER", "root")
# Try to get password from MYSQL_ROOT_PASSWORD first, then fall back to MYSQL_PASSWORD
DB_PASSWORD = os.getenv("MYSQL_ROOT_PASSWORD", os.getenv("MYSQL_PASSWORD", "your_password"))
DB_HOST = os.getenv("MYSQL_HOST", "localhost")
DB_PORT = os.getenv("MYSQL_PORT", "3306")
DB_NAME = os.getenv("MYSQL_DATABASE", "chatbot_db")

# Create database URL - URL encode the password to handle special characters
from urllib.parse import quote_plus
DB_PASSWORD_ENCODED = quote_plus(DB_PASSWORD)

# Print the actual values for debugging (without showing the full password)
print(f"Database config values:")
print(f"  DB_USER: {DB_USER}")
print(f"  DB_PASSWORD: {'*' * len(DB_PASSWORD)}")
print(f"  DB_HOST: {DB_HOST}")
print(f"  DB_PORT: {DB_PORT}")
print(f"  DB_NAME: {DB_NAME}")

# Create the database URL with explicit parameters
DATABASE_URL = f"mysql+mysqlconnector://{DB_USER}:{DB_PASSWORD_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Log database connection info (without password)
print(f"Connecting to MySQL database: {DB_USER}@{DB_HOST}:{DB_PORT}/{DB_NAME}")

# Create SQLAlchemy engine with connection pool and retry settings
print(f"Attempting to connect with URL: mysql+mysqlconnector://{DB_USER}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}")

# Try multiple connection methods
connection_successful = False

# Method 1: Standard connection with mysqlconnector
try:
    print("Method 1: Using standard mysqlconnector...")
    engine = create_engine(
        DATABASE_URL,
        pool_size=5,
        max_overflow=10,
        pool_timeout=30,
        pool_recycle=1800,  # Recycle connections after 30 minutes
        connect_args={
            "connect_timeout": 10,  # 10 seconds connection timeout
            "use_pure": True,       # Use pure Python implementation
            "auth_plugin": 'mysql_native_password'  # Specify authentication plugin
        }
    )

    # Test connection
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print(f"Method 1 successful! Test query result: {result.scalar()}")
        connection_successful = True
except Exception as e:
    print(f"Method 1 failed: {e}")

# Method 2: Try with pymysql dialect
if not connection_successful:
    try:
        print("Method 2: Using pymysql dialect...")
        # Install pymysql if needed
        import importlib.util
        if importlib.util.find_spec("pymysql") is None:
            import subprocess
            print("Installing pymysql...")
            subprocess.check_call(["pip", "install", "pymysql"])

        pymysql_url = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        engine = create_engine(
            pymysql_url,
            pool_size=5,
            max_overflow=10
        )

        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print(f"Method 2 successful! Test query result: {result.scalar()}")
            connection_successful = True
    except Exception as e:
        print(f"Method 2 failed: {e}")

# Method 3: Try with direct connection parameters
if not connection_successful:
    try:
        print("Method 3: Using direct connection parameters...")
        direct_url = f"mysql+mysqlconnector://{DB_USER}:{DB_PASSWORD_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"
        engine = create_engine(
            direct_url,
            pool_size=3,
            max_overflow=5,
            connect_args={
                "user": DB_USER,
                "password": DB_PASSWORD,
                "host": DB_HOST,
                "port": int(DB_PORT),
                "database": DB_NAME
            }
        )

        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print(f"Method 3 successful! Test query result: {result.scalar()}")
            connection_successful = True
    except Exception as e:
        print(f"Method 3 failed: {e}")

# Method 4: Last resort - minimal engine
if not connection_successful:
    print("All connection methods failed. Creating minimal engine...")
    engine = create_engine(
        f"mysql+mysqlconnector://{DB_USER}:{DB_PASSWORD_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}",
        pool_size=2,
        max_overflow=2
    )
    print("Warning: Database connection not verified. Application may fail when accessing the database.")

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Function to initialize database
def init_db():
    """
    Initialize database by creating all tables.
    """
    Base.metadata.create_all(bind=engine)

# Database dependency for FastAPI
def get_db():
    """
    Get database session.

    This function is used as a dependency in FastAPI route handlers.
    It creates a new database session for each request and closes it when the request is complete.

    Yields:
        Session: Database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
