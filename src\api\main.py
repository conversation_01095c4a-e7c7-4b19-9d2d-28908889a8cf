"""
Main API Application
Contains all endpoints including demo endpoints at root.
"""

import os
import sys
import logging
import platform
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import Optional, List

from fastapi import FastAPI, Request, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tolery-main")

# ============================================================================
# BASE_URL CONFIGURATION
# ============================================================================

# Get domain and port from environment
DOMAIN = os.getenv("DOMAIN", "http://localhost")
PORT = int(os.getenv("UVICORN_PORT", 8124))

# Only include port in BASE_URL if DOMAIN is localhost
if DOMAIN == "http://localhost" or DOMAIN == "localhost":
    BASE_URL = f"{DOMAIN}:{PORT}"
else:
    BASE_URL = DOMAIN

logger.info(f"Configured BASE_URL: {BASE_URL}")

# Add project root to Python path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import processors and dependencies
try:
    from ..utils.pdf_handler import PDFProcessor
    from ..utils.image_handler import ImageProcessor
    from ..core.chatbot import text_to_cad_agent
    from ..database.database import SessionLocal, init_db
    from ..crud import chat_processing as crud
    from ..models.sessions import Session as SessionModel, ChatHistory
except ImportError:
    from src.utils.pdf_handler import PDFProcessor
    from src.utils.image_handler import ImageProcessor
    from src.core.chatbot import text_to_cad_agent
    from src.database.database import SessionLocal, init_db
    from src.crud import chat_processing as crud
    from src.models.sessions import Session as SessionModel, ChatHistory

# Initialize processors
pdf_processor = PDFProcessor(cad_agent=text_to_cad_agent)
image_processor = ImageProcessor(cad_agent=text_to_cad_agent)

# Initialize database
init_db()

# Create FastAPI app
app = FastAPI(
    title="Tolery CAD Generation API",
    description="Main application with all endpoints including demo endpoints at root",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files and templates setup
templates_dir = project_root / 'templates'
static_dir = project_root / 'static'

try:
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    templates = Jinja2Templates(directory=str(templates_dir))
except Exception as e:
    logger.warning(f"Could not mount static files or templates: {e}")
    templates = None

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class ChatRequest(BaseModel):
    """Request model for the /api/chat endpoint."""
    message: str = Field(..., description="The user's message/request")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")

class PDFProcessingResponse(BaseModel):
    """Response model for PDF processing."""
    success: bool
    message: str
    session_id: Optional[str] = None

class ImageProcessingResponse(BaseModel):
    """Response model for image processing."""
    success: bool
    message: str
    session_id: Optional[str] = None

class GenerateRequest(BaseModel):
    """Request model for the /api/generate-gltf endpoint."""
    prompt: str = Field(..., description="The text prompt describing the 3D model to generate")
    is_edit_request: bool = Field(False, description="Whether this is a request to edit existing code")

class UpdateCodeRequest(BaseModel):
    """Request model for the /api/update-latest-code endpoint."""
    code: str
    session_id: Optional[str] = None

class ChatMessage(BaseModel):
    """Chat message model."""
    timestamp: str
    role: str
    content: str

class ChatHistoryResponse(BaseModel):
    """Chat history response model."""
    session_id: str
    messages: List[ChatMessage]
    total_messages: int

# ============================================================================
# ROOT ENDPOINTS
# ============================================================================

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    if templates:
        return templates.TemplateResponse("index.html", {"request": request})
    else:
        return HTMLResponse("""
        <html>
            <head><title>Tolery CAD API</title></head>
            <body>
                <h1>Tolery CAD Generation API</h1>
                <p>API Documentation:</p>
                <ul>
                    <li><a href="/docs">API Documentation</a></li>
                    <li><a href="/api-test/docs">API-TEST Documentation</a></li>
                    <li><a href="/api-production/docs">API-PRODUCTION Documentation</a></li>
                </ul>
            </body>
        </html>
        """)

@app.get("/api/health")
async def health_check():
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0",
        "environment": {
            "python_version": platform.python_version(),
            "system": platform.system(),
            "processor": platform.processor(),
            "pdf_processor_available": pdf_processor.client is not None
        }
    }

# ============================================================================
# CHAT AND PROCESSING ENDPOINTS
# ============================================================================

@app.post("/api/chat-to-cad", summary="Process a chat message",
         description="Takes a text message, processes it, and returns generated code")
async def chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """Process a chat message and generate CAD code."""
    try:
        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=request_data.message,
            session_id=request_data.session_id,
            image_path="",
            part_file_name="part_file_name",
            export_format="obj",
            material_choice="STEEL",
            selected_feature_uuid="",
            is_edit_request=request_data.is_edit_request
        )

        # Process using our enhanced session handling in CRUD
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='web')

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing chat request: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        # Return the result dictionary
        return result

    except Exception as e:
        logger.exception(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.post("/api/process-pdf", response_model=PDFProcessingResponse, tags=["pdf"])
async def process_pdf(file: UploadFile = File(...),
                     user_input: str = Form(""),
                     session_id: Optional[str] = Form(None),
                     db: Session = Depends(get_db)):
    """Process a PDF file with OpenAI."""
    try:
        # Process the PDF using the processor's async method which handles session properly
        success, message, final_session_id = await pdf_processor.process_uploaded_file(db, file, user_input, session_id)

        return PDFProcessingResponse(success=success, message=message, session_id=final_session_id)

    except Exception as e:
        logger.exception(f"Error processing PDF: {str(e)}")
        return PDFProcessingResponse(success=False, message=f"Error processing PDF: {str(e)}", session_id=session_id)

@app.post("/api/process-image", response_model=ImageProcessingResponse, tags=["image"])
async def process_image(file: UploadFile = File(...),
                       user_input: str = Form(""),
                       session_id: Optional[str] = Form(None),
                       db: Session = Depends(get_db)):
    """Process an image file with OpenAI Vision API."""
    try:
        # Process the image using the processor's async method if available, or create temp file with session
        if hasattr(image_processor, 'process_uploaded_file'):
            success, message, final_session_id = await image_processor.process_uploaded_file(db, file, user_input, session_id)
        else:
            # Fallback: create temp file and use session_id properly
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            file_path = temp_dir / file.filename
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)

            # Generate session_id if not provided
            if not session_id:
                import random
                import uuid
                rand_digits = random.randint(100000, 999999)
                rand_uuid_hex = uuid.uuid4().hex[:6]
                session_id = f"session_{rand_uuid_hex}_{rand_digits}"

            # Process the image with proper session_id
            success, message = image_processor.process_image(db, session_id, str(file_path), user_input)
            final_session_id = session_id

            # Clean up
            if file_path.exists():
                file_path.unlink()

        return ImageProcessingResponse(success=success, message=message, session_id=final_session_id)

    except Exception as e:
        logger.exception(f"Error processing image: {str(e)}")
        return ImageProcessingResponse(success=False, message=f"Error processing image: {str(e)}", session_id=session_id)

@app.post("/api/process-multi-file", response_model=dict, tags=["files"])
async def process_multi_file(
    pdf_file: Optional[UploadFile] = File(None),
    image_file: Optional[UploadFile] = File(None),
    user_input: str = Form(""),
    session_id: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """Process multiple files (PDF and/or image) together with session context."""
    if not pdf_file and not image_file:
        raise HTTPException(status_code=400, detail="At least one file (PDF or image) must be provided")
    
    # Generate session_id if not provided
    if not session_id:
        import random
        import uuid
        rand_digits = random.randint(100000, 999999)
        rand_uuid_hex = uuid.uuid4().hex[:6]
        session_id = f"session_{rand_uuid_hex}_{rand_digits}"
    
    results = {"session_id": session_id}
    
    # Process PDF if provided
    if pdf_file:
        logger.info(f"Processing PDF in multi-file request: {pdf_file.filename}")
        try:
            success, message, final_session_id = await pdf_processor.process_uploaded_file(db, pdf_file, user_input, session_id)
            results["pdf"] = {"success": success, "message": message, "session_id": final_session_id}
            # Update session_id to the one used by PDF processor
            session_id = final_session_id
        except Exception as e:
            logger.error(f"Error processing PDF in multi-file request: {e}")
            results["pdf"] = {"success": False, "message": f"Error processing PDF: {str(e)}", "session_id": session_id}
    
    # Process image if provided
    if image_file:
        logger.info(f"Processing image in multi-file request: {image_file.filename}")
        try:
            success, message, final_session_id = await image_processor.process_uploaded_file(db, image_file, user_input, session_id)
            results["image"] = {"success": success, "message": message, "session_id": final_session_id}
        except Exception as e:
            logger.error(f"Error processing image in multi-file request: {e}")
            results["image"] = {"success": False, "message": f"Error processing image: {str(e)}", "session_id": session_id}
    
    # Combine results if both files were processed
    if pdf_file and image_file:
        pdf_success = results.get("pdf", {}).get("success", False)
        image_success = results.get("image", {}).get("success", False)
        
        if pdf_success and image_success:
            combined_message = f"PDF Analysis: {results['pdf']['message']}\n\nImage Analysis: {results['image']['message']}"
            results["combined"] = {"success": True, "message": combined_message}
        else:
            results["combined"] = {"success": False, "message": "One or more files failed to process"}
    
    return results

@app.post("/api/refresh_chat")
async def refresh_chat_session():
    """Refresh the chat session."""
    try:
        from src.core.chatbot import clear_chat_history
        clear_chat_history()
        return {"success": True, "message": "Chat history cleared"}
    except Exception as e:
        logger.error(f"Error refreshing chat session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/generate-cad-stream")
async def generate_cad_stream(
    message: str,
    is_edit_request: bool = False,
    session_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Generate CAD with real-time progress updates via Server-Sent Events.

    Args:
        message: User's CAD generation request
        is_edit_request: Whether this is an edit request
        session_id: Optional session ID for continuity
        db: Database session

    Returns:
        StreamingResponse: Server-Sent Events stream with progress updates
    """
    import time
    stream_start_time = time.time()
    
    logger.info(f"[SSE_START] CAD generation request: '{message[:50]}...' (edit: {is_edit_request}, session: {session_id})")
    logger.info(f"[SSE_PARAMS] Message length: {len(message)} characters")
    logger.info(f"[SSE_PARAMS] Is edit request: {is_edit_request}")
    logger.info(f"[SSE_PARAMS] Session ID provided: {session_id is not None}")

    if not message:
        logger.warning(f"[SSE_ERROR] Empty message in SSE request")
        raise HTTPException(status_code=400, detail="Message is required")

    async def event_stream():
        """Generator function for Server-Sent Events."""
        stream_id = f"stream_{int(time.time())}"
        logger.info(f"[SSE_STREAM] Starting event stream {stream_id}")
        event_count = 0
        
        try:
            logger.info(f"[SSE_STREAM] Importing streaming function for stream {stream_id}")
            try:
                from ..crud.chat_processing import generate_cad_realtime_stream
            except ImportError:
                from src.crud.chat_processing import generate_cad_realtime_stream

            logger.info(f"[SSE_STREAM] Beginning real-time CAD generation for stream {stream_id}")
            async for update in generate_cad_realtime_stream(
                db=db,
                message=message,
                is_edit_request=is_edit_request,
                session_id=session_id,
                agent=text_to_cad_agent
            ):
                event_count += 1
                logger.debug(f"[SSE_EVENT] Stream {stream_id} - Event #{event_count}: {type(update).__name__}")
                
                if "step" in update:
                    step_name = update.get("step")
                    status = update.get("status", "Processing...")
                    progress = update.get("overall_percentage", 0)
                    is_complete = update.get("is_complete", False)
                    logger.info(f"[SSE_PROGRESS] Stream {stream_id} - Step: {step_name}, Progress: {progress}%, Complete: {is_complete}")
                elif "final_result" in update:
                    logger.info(f"[SSE_FINAL] Stream {stream_id} - Received final result")
                elif "error" in update:
                    logger.error(f"[SSE_ERROR] Stream {stream_id} - Error in update: {update.get('error')}")
                
                import json
                data = json.dumps(update)
                logger.debug(f"[SSE_YIELD] Stream {stream_id} - Yielding data length: {len(data)} characters")
                yield f"data: {data}\n\n"

            stream_duration = time.time() - stream_start_time
            logger.info(f"[SSE_COMPLETE] Stream {stream_id} completed successfully in {stream_duration:.2f}s with {event_count} events")

        except Exception as e:
            stream_duration = time.time() - stream_start_time
            logger.error(f"[SSE_ERROR] Error in SSE stream {stream_id} after {stream_duration:.2f}s: {str(e)}")
            logger.error(f"[SSE_ERROR] Traceback: {__import__('traceback').format_exc()}")
            
            import json
            error_data = json.dumps({"error": str(e)})
            yield f"data: {error_data}\n\n"

    logger.info(f"[SSE_RESPONSE] Creating StreamingResponse for CAD generation")
    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@app.post("/api/generate-gltf")
async def generate_gltf(request_data: GenerateRequest):
    """Generate a 3D model in GLTF format."""
    try:
        # This is a placeholder. Implement GLTF generation logic here.
        raise HTTPException(status_code=501, detail="GLTF generation not implemented yet")
    except Exception as e:
        logger.error(f"Error generating GLTF: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# --- 3D Viewer File Serving Endpoint ---

@app.get("/api/3d-viewer/{file_path:path}", tags=["cad"])
async def serve_3d_file(file_path: str, request: Request):
    """
    Serve a file for 3D viewer (without download headers).

    Args:
        file_path (str): Path to the file relative to the project root
        request (Request): The incoming request

    Returns:
        FileResponse: The requested file for viewing

    Raises:
        HTTPException: If the file is not found
    """
    try:
        # Log the request method and path
        logger.info(f"3D viewer request: {request.method} {file_path}")

        # Handle HEAD requests - FastAPI normally only captures GET for path operations
        if request.method == "HEAD":
            logger.info("HEAD request detected, will return headers only")

        # Normalize file path - handle various input formats
        if file_path.startswith('/'):
            file_path = file_path[1:]
        
        # Handle paths with 'download' prefix that might come from _create_download_url
        if file_path.startswith('download/'):
            file_path = file_path[len('download/'):]
        
        # Common paths that users might try
        known_paths = {
            'outputs': 'outputs',
            'obj': 'outputs/obj'
        }
        
        # Normalize path to use outputs directory for common shorthands
        for prefix, replacement in known_paths.items():
            if file_path.startswith(f"{prefix}/"):
                file_path = f"{replacement}/{file_path[len(prefix)+1:]}"
        
        # Construct the full file path
        project_root = Path.cwd()
        full_path = project_root / file_path

        logger.info(f"Looking for 3D file at: {full_path}")

        # Check if the file exists
        if not full_path.exists():
            logger.error(f"3D viewer file not found: {full_path}")
            
            # Try to find file in recent outputs as fallback
            if 'obj' in file_path.lower() and '/' in file_path:
                filename = file_path.split('/')[-1]
                
                # First check if outputs/obj directory exists
                obj_dir = project_root / 'outputs' / 'obj'
                if not obj_dir.exists():
                    logger.warning(f"outputs/obj directory does not exist, creating it")
                    obj_dir.mkdir(parents=True, exist_ok=True)
                    
                # Look for recent date directories
                recent_dirs = []
                if obj_dir.exists():
                    recent_dirs = sorted([d for d in obj_dir.iterdir() if d.is_dir()], reverse=True)
                
                # If we have recent directories, check them for the file
                for recent_dir in recent_dirs[:2]:  # Check only the 2 most recent directories
                    possible_path = recent_dir / filename
                    if possible_path.exists():
                        logger.info(f"Found file in recent outputs: {possible_path}")
                        full_path = possible_path
                        break
                        
                # If no recent directory found, create a placeholder one with today's date
                if not recent_dirs:
                    today = datetime.now().strftime('%Y-%m-%d')
                    today_dir = obj_dir / today
                    today_dir.mkdir(exist_ok=True)
                    logger.info(f"Created today's directory: {today_dir}")
                    
                    # For debugging/testing, create a placeholder file
                    placeholder_path = today_dir / filename
                    if not placeholder_path.exists():
                        try:
                            with open(placeholder_path, 'w') as f:
                                f.write(f"# Placeholder OBJ file\nv 0 0 0\nv 0 0 1\nv 0 1 0\nf 1 2 3\n")
                            logger.info(f"Created placeholder OBJ file: {placeholder_path}")
                            full_path = placeholder_path
                        except Exception as e:
                            logger.error(f"Error creating placeholder file: {e}")
            
            # If still not found, raise 404
            if not full_path.exists():
                raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

        # Get the filename
        filename = full_path.name

        # Determine the media type
        media_type, _ = mimetypes.guess_type(str(full_path))
        if media_type is None:
            extension = full_path.suffix.lower()
            if extension == '.obj':
                media_type = 'text/plain'  # OBJ files are text-based
            elif extension == '.mtl':
                media_type = 'text/plain'  # Material files are text-based
            else:
                media_type = 'application/octet-stream'

        logger.info(f"Serving 3D file: {full_path} (media type: {media_type})")

        # Return the file for viewing (not download)
        return FileResponse(
            path=str(full_path),
            media_type=media_type,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
                "Access-Control-Allow-Headers": "*",
                "Cache-Control": "public, max-age=3600"  # Cache for 1 hour
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving 3D file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error serving 3D file: {str(e)}")

# Add OPTIONS handler for the 3D viewer endpoint to support CORS preflight requests
@app.options("/api/3d-viewer/{file_path:path}", tags=["cad"])
async def serve_3d_file_options(file_path: str):
    """
    Handle OPTIONS requests for the 3D viewer endpoint.
    """
    return {
        "headers": {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Max-Age": "86400",  # 24 hours
        }
    }

# Add HEAD handler for the 3D viewer endpoint
@app.head("/api/3d-viewer/{file_path:path}", tags=["cad"])
async def serve_3d_file_head(file_path: str, request: Request):
    """
    Handle HEAD requests for the 3D viewer endpoint.
    Same as GET but only returns headers.
    """
    return await serve_3d_file(file_path, request)

# ============================================================================
# CHAT HISTORY ENDPOINTS
# ============================================================================

@app.post("/api/update-latest-code")
async def update_latest_code(request_data: UpdateCodeRequest, db: Session = Depends(get_db)):
    """Update the latest code for a session."""
    try:
        # Get the session
        session = db.query(SessionModel).filter(SessionModel.session_id == request_data.session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get the latest chat history entry
        latest_entry = db.query(ChatHistory).filter(
            ChatHistory.session_id == request_data.session_id
        ).order_by(ChatHistory.id.desc()).first()

        if not latest_entry:
            # Create a new chat history entry
            latest_entry = ChatHistory(
                session_id=request_data.session_id,
                message="Code update",
                response="Code updated via API",
                lasted_code=request_data.code
            )
            db.add(latest_entry)
        else:
            # Update the existing entry
            latest_entry.lasted_code = request_data.code

        db.commit()
        return {"success": True, "message": "Code updated successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating code: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating code: {str(e)}")

# ============================================================================
# MOUNT API-PRODUCTION and API-TEST
# ============================================================================

# Import and mount API-PRODUCTION
try:
    from .production.main import app as api_production_app
except ImportError:
    from src.api.production.main import app as api_production_app

app.mount("/api-production", api_production_app)

# Import and mount API-TEST
try:
    from .test.main import app as api_test_app
except ImportError:
    from src.api.test.main import app as api_test_app

app.mount("/api-test", api_test_app)

# ============================================================================
# DOWNLOAD ROUTER FOR FILE DOWNLOADS
# ============================================================================

# Import and mount download router for file downloads
try:
    from .routes.cad import download_router
except ImportError:
    from src.api.routes.cad import download_router

app.include_router(download_router)
