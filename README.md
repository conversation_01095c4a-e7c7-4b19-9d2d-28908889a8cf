# Tolery API

## Prerequisites

- Git
- Python (Specify recommended version, e.g., 3.9+)
- <PERSON><PERSON> and <PERSON><PERSON> Compose

## Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd <repository-name>
    ```

2.  **Create environment file (.env):**

    ```
    UVICORN_PORT=
    UVICORN_WORKERS=
    #DOMAIN=http://yourdomain.com

    MYSQL_ROOT_PASSWORD=
    MYSQL_DATABASE=
    MYSQL_USER=
    MYSQL_PASSWORD=
    MYSQL_HOST=
    MYSQL_PORT=

    OPENAI_API_KEY=
    ```

3.  **Install required libraries:**
    ```bash
    pip install -r requirements.txt
    ```

## Running the application

1.  **Start Docker Compose:**
    Ensure Docker is installed and running. Then, start the necessary services (like the database) using Docker Compose:

    ```bash
    docker-compose up -d
    ```

    _Wait for the services to be fully up and running._

2.  **Run the Python application:**
    Start the main application:
    ```bash
    src\rag\build_main_index.py
    python run.py
    ```

## Accessing the application

Once the application is running, you can access it via your browser at:
[http://localhost:8000](http://localhost:8000) (or another port if configured in your `.env` file).

## Stopping the application

1.  **Stop the Python application:**
    Press `Ctrl+C` in the terminal where `python run.py` is running.

2.  **Stop Docker Compose services:**
    ```bash
    docker-compose down
    ```
