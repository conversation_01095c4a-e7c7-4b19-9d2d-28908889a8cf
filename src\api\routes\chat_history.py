"""
Chat History Router for API
Handles retrieving chat history for sessions.
"""

import logging
from typing import List, Optional
from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

# Configure logging
logger = logging.getLogger(__name__)

# Import database and models
try:
    from ...database.database import get_db
    from ...models.sessions import Chat<PERSON><PERSON>ory
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import get_db
    from src.models.sessions import ChatHistory

# Create router
router = APIRouter(
    prefix="/api",
    tags=["chat-history"],
    responses={404: {"description": "Not found"}},
)

# Response Models
class ChatMessage(BaseModel):
    """Chat message model."""
    timestamp: str
    role: str
    content: str

class ChatHistoryResponse(BaseModel):
    """Chat history response model."""
    session_id: str
    messages: List[ChatMessage]
    total_messages: int

@router.get("/chat-history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """Retrieve chat history for any session (PDF, Image, or Chat)."""
    logger.info(f"Getting chat history for session: {session_id}")

    try:
        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        if not chat_entries:
            return ChatHistoryResponse(
                session_id=session_id,
                messages=[],
                total_messages=0
            )

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="human",
                    content=entry.message
                ))

            # Add assistant response
            if entry.agent_response:
                messages.append(ChatMessage(
                    timestamp=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    role="assistant",
                    content=entry.agent_response
                ))

        return ChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error retrieving chat history for session {session_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve chat history: {str(e)}") 