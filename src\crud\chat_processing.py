"""
CRUD operations for chat processing and request handling.
"""
from sqlalchemy.orm import Session
from fastapi import HTTP<PERSON>xception
from typing import Optional
import logging
import uuid
import random
import re
import json
import pprint
import langdetect # For language detection
import time
import traceback

import asyncio # Added for async operations
from ..models.sessions import Session as SessionModel, ChatHistory
from ..schemas.sessions import ChatRequest, ChatResponse
from ..core.text_to_cad_agent import TextToCADAgent
from .sessions import create_session, get_session_by_id, update_session, get_latest_code, add_chat_history_entry
from ..utils.web_search_handler import WebSearchProcessor

logger = logging.getLogger(__name__)

# Initialize web search processor
web_search_processor = WebSearchProcessor()

def detect_language(text):
    """Detect language from input text"""
    try:
        return langdetect.detect(text)
    except:
        return "en"  # Default to English if detection fails

def get_success_message(user_text):
    """Return a success message based on the user's language"""
    lang = detect_language(user_text)

    success_messages = {
        "en": "FreeCAD code generation successful.",
        "vi": "Tạo mã FreeCAD thành công.",
        "fr": "Génération de code FreeCAD réussie.",
        "es": "Generación de código FreeCAD exitosa.",
        "de": "FreeCAD-Code erfolgreich generiert.",
        "zh-cn": "FreeCAD代码生成成功。",
        "ja": "FreeCADコードの生成に成功しました。",
        "ko": "FreeCAD 코드 생성 성공.",
    }

    return success_messages.get(lang, success_messages["en"])

def handle_chat_request(
    db: Session,
    chat_req: ChatRequest,
    agent,
    request_origin: str = 'api'
) -> ChatResponse:
    """
    Handle chat request with session management and web search integration.
    """
    start_time = time.time()
    logger.info(f"[FLOW] Starting chat request handling - Origin: {request_origin}")
    logger.info(f"[FLOW] Request details: message='{chat_req.message[:100]}...', session_id={chat_req.session_id}, is_edit={chat_req.is_edit_request}")
    
    print("\n" + "="*80)
    print(f"[DEBUG] CHATBOT REQUEST INFO (Origin: {request_origin})")
    print("-"*80)
    print(f"Message: {chat_req.message}")
    print(f"Session ID: {chat_req.session_id}")
    print(f"Is Edit Request: {chat_req.is_edit_request}")

    if hasattr(chat_req, 'image_path') and chat_req.image_path:
        print(f"Image Path: {chat_req.image_path}")
        logger.info(f"[FLOW] Image path provided: {chat_req.image_path}")
    if hasattr(chat_req, 'part_file_name') and chat_req.part_file_name:
        print(f"Part File Name: {chat_req.part_file_name}")
        logger.info(f"[FLOW] Part file name: {chat_req.part_file_name}")
    if hasattr(chat_req, 'export_format') and chat_req.export_format:
        print(f"Export Format: {chat_req.export_format}")
        logger.info(f"[FLOW] Export format: {chat_req.export_format}")
    if hasattr(chat_req, 'material_choice') and chat_req.material_choice:
        print(f"Material Choice: {chat_req.material_choice}")
        logger.info(f"[FLOW] Material choice: {chat_req.material_choice}")
    if hasattr(chat_req, 'selected_feature_uuid') and chat_req.selected_feature_uuid:
        print(f"Selected Feature UUID: {chat_req.selected_feature_uuid}")
        logger.info(f"[FLOW] Selected feature UUID: {chat_req.selected_feature_uuid}")

    try:
        logger.info(f"[SESSION] Resolving session ID for request")
        session_id = _resolve_session_id(db, chat_req)
        logger.info(f"[SESSION] Resolved session ID: {session_id}")
        
        latest_code = get_latest_code(db, session_id)
        if latest_code:
            print(f"Latest Code Available: Yes (Length: {len(latest_code)} characters)")
            logger.info(f"[SESSION] Found latest code in DB - Length: {len(latest_code)} characters")
        else:
            print(f"Latest Code Available: No")
            logger.info(f"[SESSION] No latest code found in database")

        try:
            state = agent._get_session_state(session_id)
            if 'latest_requirements' in state and state['latest_requirements']:
                print("-"*80)
                print("Latest RAG Context:")
                try:
                    print(json.dumps(state['latest_requirements'].dict(), indent=2))
                    logger.info(f"[AGENT_STATE] Retrieved RAG context for session {session_id}")
                except:
                    print(pprint.pformat(state['latest_requirements'], indent=2))
                    logger.info(f"[AGENT_STATE] Retrieved RAG context (pformat) for session {session_id}")
            else:
                logger.info(f"[AGENT_STATE] No latest requirements found in agent state for session {session_id}")
        except Exception as e:
            print(f"Error accessing agent state: {str(e)}")
            logger.warning(f"[AGENT_STATE] Error accessing agent state for session {session_id}: {str(e)}")

        print("="*80 + "\n")

        logger.info(f"[SESSION] Ensuring session exists in database")
        session = get_session_by_id(db, session_id)
        if not session:
            session_name = chat_req.message[:50].strip() if chat_req.message else "User Session"
            session = create_session(db, session_id, session_name)
            logger.info(f"[SESSION] Created new session: {session_id} with name: {session_name}")
        else:
            logger.info(f"[SESSION] Using existing session: {session_id}")

        chat_req.session_id = session_id

        is_edit_request = chat_req.is_edit_request
        logger.info(f"[EDIT_MODE] Is edit request: {is_edit_request}")

        if is_edit_request:
            logger.info(f"[EDIT_MODE] Processing edit request - retrieving latest code")
            db_latest_code = get_latest_code(db, session_id)
            if db_latest_code:
                logger.info(f"[EDIT_MODE] Retrieved latest code from database - Length: {len(db_latest_code)} characters")
                agent._update_session_state(session_id, latest_code=db_latest_code)
                logger.info(f"[EDIT_MODE] Synced database latest_code to agent session state for {session_id}")
            else:
                logger.warning(f"[EDIT_MODE] Edit mode requested but no latest code found in database for session {session_id}")

        logger.info(f"[WEB_SEARCH] Checking for web search requirements")
        processed_message = chat_req.message
        web_metadata = {}

        if web_search_processor and web_search_processor.is_web_search_request(chat_req.message):
            logger.info(f"[WEB_SEARCH] Web search request detected for session {session_id}")

            has_urls, enhanced_text, metadata = web_search_processor.process_text_with_urls(chat_req.message)

            if has_urls:
                logger.info(f"[WEB_SEARCH] Processed {metadata['successful_extractions']}/{metadata['urls_found']} URLs successfully")
                processed_message = enhanced_text
                web_metadata = metadata
            else:
                logger.info(f"[WEB_SEARCH] No valid URLs found for web search in session {session_id}")
        else:
            logger.info(f"[WEB_SEARCH] No web search requirements detected for session {session_id}")

        logger.info(f"[AGENT] Starting agent processing for session {session_id}")
        agent_start_time = time.time()
        
        agent_result = agent.process_request(
            user_text=processed_message,
            is_edit_request=is_edit_request,
            request_origin=request_origin,
            session_id=session_id
        )
        
        agent_duration = time.time() - agent_start_time
        logger.info(f"[AGENT] Agent processing completed in {agent_duration:.2f}s for session {session_id}")

        print("\n" + "="*80)
        print(f"[DEBUG] CHATBOT RESPONSE SUMMARY (Origin: {request_origin})")
        print("-"*80)
        if "code" in agent_result and agent_result["code"]:
            print(f"Generated Code: Yes (Length: {len(agent_result['code'])} characters)")
            logger.info(f"[AGENT_RESULT] Code generated - Length: {len(agent_result['code'])} characters")
        else:
            print(f"Generated Code: No")
            logger.info(f"[AGENT_RESULT] No code generated")

        if "message" in agent_result and agent_result["message"]:
            print(f"Response Message: {agent_result['message'][:200]}...")
            logger.info(f"[AGENT_RESULT] Response message present - Length: {len(agent_result['message'])} characters")
        elif "error" in agent_result and agent_result["error"]:
            print(f"Error: {agent_result['error']}")
            logger.error(f"[AGENT_RESULT] Agent returned error: {agent_result['error']}")

        if "obj_path" in agent_result and agent_result["obj_path"]:
            print(f"OBJ Export Path: {agent_result['obj_path']}")
            logger.info(f"[AGENT_RESULT] OBJ export path: {agent_result['obj_path']}")
        if "step_path" in agent_result and agent_result["step_path"]:
            print(f"STEP Export Path: {agent_result['step_path']}")
            logger.info(f"[AGENT_RESULT] STEP export path: {agent_result['step_path']}")
        if "gltf_path" in agent_result and agent_result["gltf_path"]:
            print(f"GLTF Export Path: {agent_result['gltf_path']}")
            logger.info(f"[AGENT_RESULT] GLTF export path: {agent_result['gltf_path']}")
        print("="*80 + "\n")

        if web_metadata:
            agent_result["web_search_metadata"] = web_metadata
            logger.info(f"[WEB_SEARCH] Added web search metadata with {len(web_metadata.get('web_contents', []))} extracted contents")

        logger.info(f"[RESPONSE] Processing agent result and creating response")
        response = _process_agent_result(db, chat_req, agent_result, session_id)
        
        total_duration = time.time() - start_time
        logger.info(f"[FLOW] Chat request handling completed in {total_duration:.2f}s for session {session_id}")
        
        return response

    except Exception as e:
        total_duration = time.time() - start_time
        logger.error(f"[FLOW] Error in chat request handling after {total_duration:.2f}s: {str(e)}")
        logger.error(f"[FLOW] Traceback: {traceback.format_exc()}")
        raise


def _resolve_session_id(db: Session, chat_req: ChatRequest) -> str:
    """
    Resolve session ID - create new if not provided.
    """
    logger.info(f"[SESSION_RESOLVE] Starting session ID resolution")
    session_id = chat_req.session_id
    logger.info(f"[SESSION_RESOLVE] Input session_id: {session_id}")

    if session_id and session_id != "":
        logger.info(f"[SESSION_RESOLVE] Validating provided session_id: {session_id}")
        existing_session = db.query(SessionModel).filter(
            SessionModel.session_id == session_id
        ).first()
        if existing_session:
            logger.info(f"[SESSION_RESOLVE] Found existing session: {session_id}")
            return session_id
        else:
            logger.warning(f"[SESSION_RESOLVE] Provided session_id {session_id} does not exist, but will use it for continuity")
            return session_id

    if chat_req.message:
        logger.info(f"[SESSION_RESOLVE] Checking message for existing session ID pattern")
        session_pattern = re.compile(r'session_[a-f0-9]{6}_\d{6}')
        session_matches = session_pattern.findall(chat_req.message)
        if session_matches:
            potential_session_id = session_matches[0]
            logger.info(f"[SESSION_RESOLVE] Found potential session ID in message: {potential_session_id}")
            existing_session = db.query(SessionModel).filter(
                SessionModel.session_id == potential_session_id
            ).first()
            if existing_session:
                logger.info(f"[SESSION_RESOLVE] Validated session ID from message: {potential_session_id}")
                return potential_session_id
            else:
                logger.warning(f"[SESSION_RESOLVE] Session ID found in message but not in database: {potential_session_id}")

    new_session_id = _generate_session_id()
    logger.info(f"[SESSION_RESOLVE] Generated new session ID: {new_session_id}")
    return new_session_id


def _generate_session_id() -> str:
    """
    Generate a new unique session ID.
    """
    rand_digits = random.randint(100000, 999999)
    rand_uuid = uuid.uuid4().hex[:6]
    session_id = f"session_{rand_uuid}_{rand_digits}"
    logger.info(f"[SESSION_GEN] Generated session ID: {session_id}")
    return session_id


def _process_agent_result(
    db: Session,
    chat_req: ChatRequest,
    agent_result: dict,
    session_id: str
) -> ChatResponse:
    """
    Process agent result and create chat response.
    """
    logger.info(f"[RESULT_PROCESS] Processing agent result for session {session_id}")
    
    if agent_result.get("message") and not agent_result.get("code"):
        chat_response_content = agent_result.get("message")
        logger.info(f"[RESULT_PROCESS] Using agent message as response (no code generated)")
    elif agent_result.get("error"):
        chat_response_content = f"Error: {agent_result.get('error')}"
        logger.error(f"[RESULT_PROCESS] Using error message as response: {agent_result.get('error')}")
    elif agent_result.get("code"):
        chat_response_content = get_success_message(chat_req.message)
        logger.info(f"[RESULT_PROCESS] Code generated successfully, using success message")
    else:
        chat_response_content = "Processing completed."
        logger.info(f"[RESULT_PROCESS] Using default completion message")

    # Add response to agent_result for database storage
    agent_result["response"] = chat_response_content

    logger.info(f"[EXPORT] Handling export paths for session {session_id}")
    obj_export_path, step_export_path = _handle_export_paths(chat_req, agent_result)
    logger.info(f"[EXPORT] Export paths determined - OBJ: {obj_export_path}, STEP: {step_export_path}")

    logger.info(f"[HISTORY] Adding entry to chat history for session {session_id}")
    _add_to_chat_history(db, session_id, chat_req, agent_result, obj_export_path, step_export_path)

    logger.info(f"[DOWNLOAD] Creating download URLs for session {session_id}")
    obj_url = _create_download_url(obj_export_path) if obj_export_path else None
    step_url = _create_download_url(step_export_path) if step_export_path else None
    
    if obj_url:
        logger.info(f"[DOWNLOAD] OBJ download URL: {obj_url}")
    if step_url:
        logger.info(f"[DOWNLOAD] STEP download URL: {step_url}")

    logger.info(f"[RESULT_PROCESS] Creating final ChatResponse for session {session_id}")
    return ChatResponse(
        chat_response=chat_response_content,
        session_id=session_id,
        obj_export=obj_url,
        step_export=step_url,
        tessellated_export=None,
        attribute_and_transientid_map=None,
        manufacturing_errors=[]
    )


def _handle_export_paths(chat_req: ChatRequest, agent_result: dict) -> tuple:
    """
    Handle export file paths based on export format.
    """
    export_format = chat_req.export_format
    obj_path = agent_result.get("obj_path")
    step_path = agent_result.get("step_path")
    
    logger.info(f"[EXPORT_PATHS] Processing export format: {export_format}")
    logger.info(f"[EXPORT_PATHS] Available paths - OBJ: {obj_path}, STEP: {step_path}")

    if export_format is None or export_format == "":
        logger.info(f"[EXPORT_PATHS] No specific format requested, returning both paths")
        return obj_path, step_path
    elif export_format.lower() == "obj":
        logger.info(f"[EXPORT_PATHS] OBJ format requested, returning OBJ path only")
        return obj_path, None
    elif export_format.lower() == "step":
        logger.info(f"[EXPORT_PATHS] STEP format requested, returning STEP path only")
        return None, step_path
    else:
        logger.warning(f"[EXPORT_PATHS] Unknown export format '{export_format}', returning both paths")
        return obj_path, step_path


def _add_to_chat_history(
    db: Session,
    session_id: str,
    chat_req: ChatRequest,
    agent_result: dict,
    obj_export_path: Optional[str],
    step_export_path: Optional[str]
):
    """
    Add entry to chat history.
    """
    logger.info(f"[CHAT_HISTORY] Adding chat history entry for session {session_id}")
    
    if agent_result.get("error"):
        output = f"ERROR_RESPONSE: {agent_result.get('error')}"
        logger.error(f"[CHAT_HISTORY] Adding error response to history: {agent_result.get('error')}")
    elif agent_result.get("code"):
        output = f"CODE_GENERATED: {agent_result.get('code')}"
        logger.info(f"[CHAT_HISTORY] Adding code generation to history - Length: {len(agent_result.get('code'))} characters")
    elif agent_result.get("message"):
        output = agent_result.get("message")
        logger.info(f"[CHAT_HISTORY] Adding message response to history - Length: {len(agent_result.get('message'))} characters")
    else:
        output = "Agent produced an unknown response structure."
        logger.warning(f"[CHAT_HISTORY] Adding unknown response structure to history")

    lasted_code = agent_result.get("code") if agent_result.get("code") else None
    export_format = chat_req.export_format or "both"
    
    logger.info(f"[CHAT_HISTORY] Export format for history: {export_format}")

    try:
        add_chat_history_entry(
            db=db,
            session_id=session_id,
            user_message=chat_req.message,
            agent_result=agent_result,
            chat_request_obj=chat_req,
            obj_export_path=obj_export_path
        )
        logger.info(f"[CHAT_HISTORY] Successfully added chat history entry for session {session_id}")
    except Exception as e:
        logger.error(f"[CHAT_HISTORY] Error adding chat history entry for session {session_id}: {str(e)}")
        logger.error(f"[CHAT_HISTORY] Traceback: {traceback.format_exc()}")

    if lasted_code:
        logger.info(f"[CHAT_HISTORY] Stored latest code in chat_history for session {session_id} ({len(lasted_code)} characters)")


def _create_download_url(file_path: str) -> str:
    """
    Create full download URL with domain for the file path.
    """
    import os
    from pathlib import Path

    # Get BASE_URL from environment or use default
    PORT = os.getenv("PORT", "8080")
    DOMAIN = os.getenv("DOMAIN", "http://localhost")

    # Only include port in BASE_URL if DOMAIN is localhost
    if DOMAIN == "http://localhost" or DOMAIN == "localhost":
        BASE_URL = f"{DOMAIN}:{PORT}"
    else:
        BASE_URL = DOMAIN

    # Convert to relative path first
    project_root = Path.cwd()
    project_root_str = str(project_root).replace('\\', '/')
    file_path_str = str(file_path).replace('\\', '/')

    if project_root_str in file_path_str:
        # Extract the part after the project root
        relative_path = file_path_str.split(project_root_str, 1)[1].lstrip('\\/')
        
        # Create full download URL
        # Convert outputs/obj/date/file.obj to /download/outputs/obj/date/file.obj format
        if relative_path.startswith('outputs/obj/'):
            # Extract filename and date directory
            path_parts = relative_path.split('/')
            if len(path_parts) >= 4:  # outputs/obj/date/filename
                date_dir = path_parts[2]
                filename = path_parts[3]
                download_url = f"{BASE_URL}/download/outputs/obj/{date_dir}/{filename}"
            else:
                download_url = f"{BASE_URL}/download/{relative_path}"
        elif relative_path.startswith('outputs/cad/'):
            # Extract filename and date directory for STEP files
            path_parts = relative_path.split('/')
            if len(path_parts) >= 4:  # outputs/cad/date/filename
                date_dir = path_parts[2]
                filename = path_parts[3]
                download_url = f"{BASE_URL}/download/outputs/step/{date_dir}/{filename}"
            else:
                download_url = f"{BASE_URL}/download/{relative_path}"
        else:
            download_url = f"{BASE_URL}/download/{relative_path}"
        
        return download_url
    else:
        # If path doesn't contain project root, treat as filename and try to construct URL
        filename = os.path.basename(file_path)
        if filename.endswith('.obj'):
            # Assume it's in today's outputs/obj directory
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            download_url = f"{BASE_URL}/download/outputs/obj/{today}/{filename}"
        elif filename.endswith('.step'):
            # Assume it's in today's outputs/cad directory
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            download_url = f"{BASE_URL}/download/outputs/step/{today}/{filename}"
        else:
            download_url = f"{BASE_URL}/download/{file_path}"
        
        return download_url


async def generate_cad_realtime_stream(
    db: Session,
    message: str,
    is_edit_request: bool,
    session_id: Optional[str],
    agent: TextToCADAgent
):
    """
    Asynchronously generates CAD and streams real-time progress updates with flexible flow.
    """
    stream_start_time = time.time()
    logger.info(f"[STREAM] Starting real-time CAD generation stream for: '{message[:50]}...'")
    logger.info(f"[STREAM] Stream parameters - edit: {is_edit_request}, session: {session_id}")

    try:
        logger.info(f"[STREAM] Resolving session ID for streaming request")
        resolved_session_id = _resolve_session_id(db, ChatRequest(message=message, session_id=session_id, is_edit_request=is_edit_request))
        logger.info(f"[STREAM] Resolved session ID: {resolved_session_id}")

        logger.info(f"[STREAM] Ensuring session exists in database")
        session_obj = get_session_by_id(db, resolved_session_id)
        if not session_obj:
            session_name = message[:50].strip() if message else "User Session"
            create_session(db, resolved_session_id, session_name)
            logger.info(f"[STREAM] Created session {resolved_session_id} during stream processing")
        else:
            logger.info(f"[STREAM] Using existing session {resolved_session_id}")

        logger.info(f"[STREAM] Checking for web search requirements")
        processed_message = message
        web_metadata = {}

        if web_search_processor and web_search_processor.is_web_search_request(message):
            logger.info(f"[STREAM_WEB] Web search request detected for session {resolved_session_id}")
            has_urls, enhanced_text, metadata = web_search_processor.process_text_with_urls(message)
            if has_urls:
                processed_message = enhanced_text
                web_metadata = metadata
                logger.info(f"[STREAM_WEB] Enhanced message with {metadata['successful_extractions']} URL extractions")
            else:
                logger.info(f"[STREAM_WEB] No valid URLs found for processing")
        else:
            logger.info(f"[STREAM_WEB] No web search requirements detected")

        logger.info(f"[STREAM] Initializing progress tracking")
        step_progress = {
            "analysis": False,
            "parameters": False,
            "generation_code": False,
            "export": False,
            "complete": False
        }

        completed_steps = 0
        total_steps = 5

        logger.info(f"[STREAM] Starting agent progress streaming for session {resolved_session_id}")
        step_count = 0
        async for progress_update in agent.process_request_with_progress(
            user_text=processed_message,
            is_edit_request=is_edit_request,
            request_origin='web',
            session_id=resolved_session_id
        ):
            step_count += 1
            logger.debug(f"[STREAM_PROGRESS] Received progress update #{step_count} for session {resolved_session_id}")
            
            if "step" in progress_update:
                step_name = progress_update["step"]
                is_complete = progress_update.get("is_complete", False)
                is_active = progress_update.get("is_active", False)
                status = progress_update.get("status", "Processing...")
                progress = progress_update.get("progress", 0)
                
                logger.info(f"[STREAM_STEP] Step: {step_name}, Complete: {is_complete}, Active: {is_active}, Progress: {progress}%")
                
                if is_complete and not step_progress.get(step_name, False):
                    step_progress[step_name] = True
                    completed_steps += 1
                    logger.info(f"[STREAM_STEP] Step {step_name} completed, {completed_steps}/{total_steps} steps done")
                
                icon = "fas fa-hourglass-start"
                if is_active:
                    if step_name == "analysis":
                        icon = "fas fa-search fa-spin"
                    elif step_name == "parameters":
                        icon = "fas fa-list-alt fa-spin"
                    elif step_name == "generation_code":
                        icon = "fas fa-code fa-spin"
                    elif step_name == "export":
                        icon = "fas fa-file-export fa-spin"
                    elif step_name == "complete":
                        icon = "fas fa-check-circle fa-spin"
                elif is_complete:
                    if step_name == "analysis":
                        icon = "fas fa-search"
                    elif step_name == "parameters":
                        icon = "fas fa-list-alt"
                    elif step_name == "generation_code":
                        icon = "fas fa-code"
                    elif step_name == "export":
                        icon = "fas fa-file-export"
                    elif step_name == "complete":
                        icon = "fas fa-check-circle"
                
                overall_percentage = min(95, (completed_steps * 100) // total_steps)
                if progress > 0:
                    overall_percentage = progress
                
                yield {
                    "step": step_name,
                    "status": status,
                    "message": "Completed" if is_complete else "Processing...",
                    "icon": icon,
                    "is_complete": is_complete,
                    "is_active": is_active,
                    "overall_percentage": overall_percentage,
                }
                
                logger.info(f"[STREAM_YIELD] Progress update: {step_name} - {status} ({overall_percentage}%)")
                
            elif "final_result" in progress_update:
                logger.info(f"[STREAM_FINAL] Received final result for session {resolved_session_id}")
                agent_result = progress_update["final_result"]
                
                if web_metadata:
                    agent_result["web_search_metadata"] = web_metadata
                    logger.info(f"[STREAM_FINAL] Added web metadata to final result")
                
                if not step_progress.get("complete", False):
                    logger.info(f"[STREAM_FINAL] Yielding completion step")
                    yield {
                        "step": "complete",
                        "status": "All processing completed!",
                        "message": "Completed",
                        "icon": "fas fa-check-circle",
                        "is_complete": True,
                        "is_active": False,
                        "overall_percentage": 100,
                    }
                
                logger.info(f"[STREAM_FINAL] Processing agent result and creating final response")
                if agent_result and not agent_result.get("error"):
                    chat_req_obj = ChatRequest(
                        message=processed_message,
                        is_edit_request=is_edit_request,
                        session_id=resolved_session_id
                    )
                    final_response = _process_agent_result(db, chat_req_obj, agent_result, resolved_session_id)
                    logger.info(f"[STREAM_FINAL] Successfully created final response for session {resolved_session_id}")
                else:
                    error_msg = agent_result.get("error", "Unknown error occurred") if agent_result else "Agent processing failed"
                    logger.error(f"[STREAM_FINAL] Error in agent result: {error_msg}")
                    final_response = ChatResponse(
                        chat_response=f"Error: {error_msg}",
                        session_id=resolved_session_id,
                        obj_export=None,
                        step_export=None,
                        tessellated_export=None,
                        attribute_and_transientid_map=None,
                        manufacturing_errors=[]
                    )

                yield {"final_response": final_response.model_dump()}
                
                stream_duration = time.time() - stream_start_time
                logger.info(f"[STREAM] Real-time CAD generation completed for session {resolved_session_id} in {stream_duration:.2f}s")
                return

    except Exception as e:
        stream_duration = time.time() - stream_start_time
        logger.error(f"[STREAM] Error in real-time CAD generation after {stream_duration:.2f}s: {str(e)}")
        logger.error(f"[STREAM] Traceback: {traceback.format_exc()}")
        yield {"error": f"Failed to generate CAD: {str(e)}"}
