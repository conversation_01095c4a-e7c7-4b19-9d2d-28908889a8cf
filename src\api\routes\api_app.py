"""
API app for routes in cad.py, sessions.py, and chat_history.py.
This creates a separate FastAPI app instance for these routes.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

# Set up logging
logger = logging.getLogger("tolery-api-routes")

# Create a separate FastAPI app for the routes
api_app = FastAPI(
    title="Tolery API Production",
    description="Production API routes for CAD, session, and chat history operations",
    version="0.2.0",
    # Configure docs URL to be at the root of the mounted path
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
api_app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from .sessions import router as sessions_router
# from .chat_history import router as chat_history_router  # Temporarily disabled
from .cad import router as cad_router
api_app.include_router(sessions_router)
# api_app.include_router(chat_history_router)  # Temporarily disabled
api_app.include_router(cad_router)

logger.info("API routes app initialized with sessions, chat history, and CAD routers")
