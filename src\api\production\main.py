import os
import logging
import time
from typing import List, Optional, Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy import or_

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tolery-api-production")

# Import database and models
try:
    from ..database.database import SessionLocal
    from ..models.sessions import Session as SessionModel, ChatHistory
    from ..crud import chat_processing as crud
    from ..core.chatbot import text_to_cad_agent
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import SessionLocal
    from src.models.sessions import Session as SessionModel, ChatHistory
    from src.crud import chat_processing as crud
    from src.core.chatbot import text_to_cad_agent

# Import routers
# PDF and Image chat routers removed from production API

# FastAPI app
app = FastAPI(
    title="Tolery API-PRODUCTION",
    description="Production API for CAD Generation System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Note: Static files and templates are handled by main app, not sub-apps

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ============================================================================
# API-PRODUCTION: CORE ENDPOINTS
# ============================================================================

# 1. SESSIONS MANAGEMENT
class SessionResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    session_name: str = Field(..., description="Session name")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: Optional[str] = Field(None, description="Last update timestamp")

class SessionListResponse(BaseModel):
    sessions: List[Dict[str, Any]] = Field(..., description="List of sessions")

@app.get("/sessions", response_model=SessionListResponse, tags=["session"])
async def get_sessions(db: Session = Depends(get_db)):
    """Get all sessions."""
    try:
        sessions = db.query(SessionModel).order_by(SessionModel.created_at.desc()).all()

        session_list = []
        for session in sessions:
            # Get the most recent chat history for this session to find part_file_name
            latest_chat = db.query(ChatHistory).filter(
                ChatHistory.session_id == session.session_id,
                ChatHistory.part_file_name.isnot(None)
            ).order_by(ChatHistory.created_at.desc()).first()
            
            part_file_name = latest_chat.part_file_name if latest_chat else None
            
            session_list.append({
                "session_id": session.session_id,
                "created_at": session.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                "last_modified": session.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if session.updated_at else None,
                "part_file_name": part_file_name
            })

        return SessionListResponse(sessions=session_list)

    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting sessions: {str(e)}")

# 2. SESSION INFO
class DocumentInfo(BaseModel):
    document_id: str = Field(..., description="Document ID")
    workspace_id: str = Field(..., description="Workspace ID")
    element_id: str = Field(..., description="Element ID")
    folder_id: str = Field(..., description="Folder ID")

class SessionInfoResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    created_at: str = Field(..., description="Creation timestamp")
    last_modified: Optional[str] = Field(None, description="Last update timestamp")
    part_file_name: Optional[str] = Field(None, description="Part file name")
    document_info: Optional[DocumentInfo] = Field(None, description="Document information")

@app.get("/sessions/{session_id}", response_model=SessionInfoResponse, tags=["session"])
async def get_session_info(session_id: str, db: Session = Depends(get_db)):
    """Get session information."""
    try:
        # Get session
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get latest chat with part_file_name
        latest_chat = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.part_file_name.isnot(None)
        ).order_by(ChatHistory.created_at.desc()).first()
        
        part_file_name = latest_chat.part_file_name if latest_chat else None

        # Create document info (placeholder values - in real implementation, this would come from document storage)
        document_info = DocumentInfo(
            document_id="35b94c1786ce15f211fa376a",
            workspace_id="acc3fe5c5bb850f4e0aa6998",
            element_id="c07c00d1d1323b9f49bd3622",
            folder_id="2dd8d7ae982d4163e732db9a"
        )

        return SessionInfoResponse(
            session_id=session.session_id,
            created_at=session.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            last_modified=session.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if session.updated_at else None,
            part_file_name=part_file_name,
            document_info=document_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting session info: {str(e)}")

# Get latest code for a session
@app.get("/sessions/{session_id}/latest-code", tags=["session"])
async def get_latest_code(session_id: str, db: Session = Depends(get_db)):
    """Get the latest generated code for a specific session."""
    try:
        logger.info(f"Fetching latest code for session: {session_id}")
        
        # First check if session exists
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            logger.error(f"Session not found: {session_id}")
            raise HTTPException(status_code=404, detail="Session not found")

        # Get the latest chat history entry with code
        latest_entry = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id,
            ChatHistory.lasted_code.isnot(None)  # Check for non-null code
        ).order_by(ChatHistory.created_at.desc()).first()
        
        if not latest_entry or not latest_entry.lasted_code:
            logger.warning(f"No code found for session {session_id}")
            return {
                "error": "No code has been generated in this session yet. Generate code first before using edit mode.",
                "latest_code": None,
                "session_id": session_id
            }
        
        logger.info(f"Found code for session {session_id}, length: {len(latest_entry.lasted_code)} characters")
        
        # Debug log - show some of the code to verify it's correct
        code_preview = latest_entry.lasted_code[:100] + "..." if latest_entry.lasted_code else "None"
        logger.info(f"Code preview for session {session_id}: {code_preview}")
        
        return {
            "latest_code": latest_entry.lasted_code,
            "session_id": session_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting latest code: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting latest code: {str(e)}")

# 3. DELETE SESSION
@app.delete("/sessions/{session_id}", tags=["session"])
async def delete_session(session_id: str, delete_type: str = "soft", db: Session = Depends(get_db)):
    """Delete a session.
    
    Args:
        session_id: The ID of the session to delete.
        delete_type: Type of deletion: 'soft' (keep in database but clear data) or 'hard' (remove from database).
                    Default is 'soft'.
    """
    try:
        # Import CRUD function
        from src.crud.sessions import delete_session as crud_delete_session
        
        # Check if session exists
        session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Process the delete based on delete_type
        if delete_type not in ["soft", "hard"]:
            raise HTTPException(status_code=400, detail="Invalid delete_type. Use 'soft' or 'hard'.")
            
        if delete_type == "soft":
            # Clear chat history but keep the session
            db.query(ChatHistory).filter(ChatHistory.session_id == session_id).delete()
            db.commit()
            return {"success": True, "message": f"Session {session_id} data cleared (soft delete)"}
        else:  # delete_type == "hard"
            # Delete chat history
            db.query(ChatHistory).filter(ChatHistory.session_id == session_id).delete()
            
            # Delete session
            db.delete(session)
            db.commit()
            return {"success": True, "message": f"Session {session_id} permanently deleted (hard delete)"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

# 4. GET EXPORT
class ExportResponseItem(BaseModel):
    session_id: str = Field(..., description="Session ID")
    export_format: str = Field(..., description="Export format (obj or step)")
    export_link: str = Field(..., description="Link to the exported file")
    export_time: str = Field(..., description="Timestamp of the export")

class ExportResponse(BaseModel):
    exports: List[ExportResponseItem] = Field(..., description="List of export files")

# Helper function to clean path and create URL
def create_download_url(file_path, base_url):
    """
    Create a standardized download URL for exported files.
    Format: https://domain.com/download/outputs/format/YYYY-MM-DD/filename_YYYYMMDDHHMMSS.ext
    """
    import os
    import re
    from datetime import datetime
    
    if file_path.startswith(('http://', 'https://')):
        return file_path
        
    # Clean path - remove backslashes and common prefixes
    clean_path = file_path.replace('\\', '/')
    clean_path = clean_path.replace('D:/DFM_ATN_TOLERY/', '')
    clean_path = clean_path.replace('D:/DFM_ATN_TOLERY', '')
    
    # Remove leading slash if present
    if clean_path.startswith('/'):
        clean_path = clean_path[1:]
    
    # Extract filename and extension
    filename = os.path.basename(clean_path)
    _, ext = os.path.splitext(filename)
    ext = ext.lower().replace('.', '')  # Get extension without dot and lowercase it
    
    # Get today's date
    today = datetime.now().strftime('%Y-%m-%d')
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    
    # Check if the filename already contains a timestamp
    # If not, add timestamp to filename
    if not re.search(r'\d{14}', filename):
        base_filename = os.path.splitext(filename)[0]
        filename = f"{base_filename}_{timestamp}.{ext}"
    
    # Determine the export format from the extension or path
    if ext == 'obj':
        format_dir = 'obj'
    elif ext == 'step' or ext == 'stp':
        format_dir = 'step'
    elif ext == 'dxf':
        format_dir = 'dxf'
    else:
        # Try to extract format from path if it contains /obj/, /step/, etc.
        if '/obj/' in clean_path or '\\obj\\' in clean_path:
            format_dir = 'obj'
        elif '/step/' in clean_path or '\\step\\' in clean_path:
            format_dir = 'step'
        elif '/dxf/' in clean_path or '\\dxf\\' in clean_path:
            format_dir = 'dxf'
        else:
            # Default to a generic "exports" directory
            format_dir = 'exports'
    
    # Construct the standardized URL
    download_url = f"{base_url}/download/outputs/{format_dir}/{today}/{filename}"
    
    # Make sure the URL is properly formatted
    download_url = download_url.replace("//download", "/download")
    
    return download_url

@app.get("/api/get-export", response_model=ExportResponse, tags=["session"])
async def get_export(session_id: str, export_format: Optional[str] = None, db: Session = Depends(get_db)):
    """Get export files for a session.
    
    If export_format is not provided, returns all export files available (obj, step, dxf).
    If export_format is provided, returns only exports of that format (obj, step, or dxf).
    """
    try:
        # Get domain from environment variables
        import os
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Get domain from .env file or use default
        DOMAIN = os.getenv("DOMAIN", "http://localhost")
        PORT = os.getenv("PORT", "8080")
        
        # Only include port in BASE_URL if DOMAIN is localhost
        if DOMAIN == "http://localhost" or DOMAIN == "localhost":
            BASE_URL = f"{DOMAIN}:{PORT}"
        else:
            BASE_URL = DOMAIN
            
        # Base query for the session
        query = db.query(ChatHistory).filter(ChatHistory.session_id == session_id)
        
        # Filter by export format if specified
        if export_format:
            if export_format.lower() not in ["obj", "step", "dxf"]:
                raise HTTPException(status_code=400, detail="Invalid export format. Use 'obj', 'step', or 'dxf'.")
            
            if export_format.lower() == "obj":
                query = query.filter(ChatHistory.obj_export.isnot(None))
            elif export_format.lower() == "step":
                query = query.filter(ChatHistory.step_export.isnot(None))
            # Only filter by dxf_export if the column exists
            elif export_format.lower() == "dxf":
                try:
                    query = query.filter(ChatHistory.dxf_export.isnot(None))
                except Exception as e:
                    logger.error(f"DXF export not supported: {e}")
                    raise HTTPException(status_code=400, detail="DXF export not supported in this version")
        else:
            # If no format specified, get entries with any export (only check columns that exist)
            filters = []
            filters.append(ChatHistory.obj_export.isnot(None))
            filters.append(ChatHistory.step_export.isnot(None))
            
            # Check if dxf_export attribute exists
            try:
                if hasattr(ChatHistory, 'dxf_export'):
                    filters.append(ChatHistory.dxf_export.isnot(None))
            except Exception:
                # Ignore if dxf_export doesn't exist
                pass
                
            query = query.filter(or_(*filters))
        
        # Get all matching entries, ordered by most recent first
        entries = query.order_by(ChatHistory.created_at.desc()).all()
        
        if not entries:
            if export_format:
                raise HTTPException(status_code=404, detail=f"No {export_format} export found for this session")
            else:
                raise HTTPException(status_code=404, detail="No exports found for this session")
        
        # Prepare the response
        export_items = []
        
        for entry in entries:
            # Check for OBJ export
            if entry.obj_export and (export_format is None or export_format.lower() == "obj"):
                export_link = create_download_url(entry.obj_export, BASE_URL)
                
                export_items.append(ExportResponseItem(
                    session_id=session_id,
                    export_format="obj",
                    export_link=export_link,
                    export_time=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f")
                ))
            
            # Check for STEP export
            if entry.step_export and (export_format is None or export_format.lower() == "step"):
                export_link = create_download_url(entry.step_export, BASE_URL)
                
                export_items.append(ExportResponseItem(
                    session_id=session_id,
                    export_format="step",
                    export_link=export_link,
                    export_time=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f")
                ))
                
            # Check for DXF export - carefully check if attribute exists
            try:
                if hasattr(entry, 'dxf_export') and entry.dxf_export and (export_format is None or export_format.lower() == "dxf"):
                    export_link = create_download_url(entry.dxf_export, BASE_URL)
                    
                    export_items.append(ExportResponseItem(
                        session_id=session_id,
                        export_format="dxf",
                        export_link=export_link,
                        export_time=entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f")
                    ))
            except Exception as e:
                # Just log the error and continue, don't break the entire endpoint
                logger.error(f"Error processing DXF export: {e}")
        
        return ExportResponse(exports=export_items)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exports: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting exports: {str(e)}")

# 5. CHAT HISTORY (Unified for PDF, Image, Chat)
class ChatHistoryResponse(BaseModel):
    session_id: str = Field(..., description="Session ID")
    messages: List[dict] = Field(..., description="List of chat messages")
    total_messages: int = Field(..., description="Total number of messages")

@app.get("/api/chat-history/{session_id}", response_model=ChatHistoryResponse, tags=["session"])
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """Retrieve chat history for any session (PDF, Image, or Chat)."""
    logger.info(f"Getting chat history for session: {session_id}")

    try:
        # Get domain for URL construction
        import os
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Get domain from .env file or use default
        DOMAIN = os.getenv("DOMAIN", "http://localhost")
        PORT = os.getenv("PORT", "8080")
        
        # Only include port in BASE_URL if DOMAIN is localhost
        if DOMAIN == "http://localhost" or DOMAIN == "localhost":
            BASE_URL = f"{DOMAIN}:{PORT}"
        else:
            BASE_URL = DOMAIN

        # Query chat history for the session
        chat_entries = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).all()

        messages = []
        for entry in chat_entries:
            # Add user message
            if entry.message:
                messages.append({
                    "timestamp": entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "human",
                    "content": entry.message
                })

            # Add AI response if available
            if entry.response:
                ai_msg = {
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.response
                }
                messages.append(ai_msg)
            elif entry.output:
                # Fallback to output if no response
                ai_msg = {
                    "timestamp": entry.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f") if entry.updated_at else entry.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "role": "ai",
                    "content": entry.output
                }
                messages.append(ai_msg)

        return ChatHistoryResponse(
            session_id=session_id,
            messages=messages,
            total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting chat history: {str(e)}")

# 6. CHAT (Regular text chat)
class ChatRequest(BaseModel):
    message: str = Field(..., description="The user's message/request")
    image_path: str = Field("", description="Path to the uploaded image (if any)")
    session_id: str = Field("", description="Session ID for conversation continuity")
    part_file_name: str = Field("part_file_name", description="Name of the part file")
    export_format: str = Field("string", description="Format for export (obj, step, dxf)")
    material_choice: str = Field("STEEL", description="Material choice for the part")
    selected_feature_uuid: str = Field("", description="UUID of selected feature (if any)")

@app.post("/api/chat", tags=["cad"])
async def chat(request_data: ChatRequest, db: Session = Depends(get_db)):
    """Process a chat message and generate CAD code with session continuity."""
    user_message = request_data.message
    session_id = request_data.session_id
    
    logger.info(f"Chat request received: '{user_message[:50]}...' (session_id: {session_id})")

    if not user_message:
        logger.warning("Empty message received")
        raise HTTPException(status_code=400, detail="No message provided")

    try:
        # Create a ChatRequest for the API
        from src.schemas.sessions import ChatRequest as ApiChatRequest
        api_request = ApiChatRequest(
            message=user_message,
            session_id=session_id,
            image_path=request_data.image_path,
            part_file_name=request_data.part_file_name,
            export_format=request_data.export_format,
            material_choice=request_data.material_choice,
            selected_feature_uuid=request_data.selected_feature_uuid,
            is_edit_request=False,  # Default to false for regular chat endpoint
        )

        # Process using our enhanced session handling in CRUD
        result = crud.handle_chat_request(db, api_request, text_to_cad_agent, request_origin='web')

        # Check for errors
        if isinstance(result, dict) and result.get("error"):
            logger.error(f"Error processing chat request: {result['error']}")
            raise HTTPException(status_code=500, detail=result["error"])

        # Log success
        if "code" in result and result["code"]:
            logger.info(f"Successfully generated code ({len(result['code'])} characters)")

        # Return the result dictionary
        return result

    except Exception as e:
        logger.exception(f"Unexpected error in chat endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

# Root endpoint is handled by main app

# PDF and Image Chat routes removed from production API

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8124)
