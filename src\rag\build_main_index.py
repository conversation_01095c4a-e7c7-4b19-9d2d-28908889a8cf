import os
import sys

# Ensure src is in path for direct execution
if __name__ == '__main__' and (__package__ is None or __package__ == ''):
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from src.rag.chunking import chunk_guide_en, chunk_example_txt, chunk_chat_history, chunk_all_class_rules
from src.rag.vector_store import build_and_save_index, INDEX_DIR, METADATA_FILE_PATH
from src.rag.embedding import get_embedding_model # To initialize model

def main():
    print("Building the main RAG index...")

    # 1. Initialize embedding model (important before any embedding operation)
    print("Initializing embedding model...")
    try:
        get_embedding_model()
        print("Embedding model initialized.")
    except Exception as e:
        print(f"Fatal: Could not initialize embedding model: {e}")
        return

    # 2. Load and chunk all data sources
    print("Loading and chunking data sources...")
    guide_docs = chunk_guide_en()
    print(f"Loaded {len(guide_docs)} chunks from guide_en.txt")
    
    example_docs_raw = chunk_example_txt()
    example_docs = []
    for doc in example_docs_raw:
        example_docs.append({
            "text_content": f"Example Script: {doc['script_name']}\n\n{doc['script_content']}",
            **doc 
        })
    print(f"Loaded {len(example_docs)} chunks from example.txt")

    # NEW: Load class rules
    rules_docs = chunk_all_class_rules()
    print(f"Loaded {len(rules_docs)} rule chunks from all classes")

    chat_docs_raw = chunk_chat_history()
    chat_docs = []
    for doc in chat_docs_raw:
        chat_docs.append({
            "text_content": f"User: {doc['user_message']}\nGenerated Code:\n{doc['code_content']}",
            **doc
        })
    print(f"Loaded {len(chat_docs)} chunks from chat_history.json")

    # Include rules in the index
    all_documents_for_index = guide_docs + example_docs + rules_docs + chat_docs
    print(f"Total documents to index: {len(all_documents_for_index)}")

    if not all_documents_for_index:
        print("No documents found to build the index. Exiting.")
        return

    # 3. Define the main index name
    main_index_name = "main_rag_index.index"
    
    # Clean up old index and metadata if they exist for a fresh build
    main_index_file_path = os.path.join(INDEX_DIR, main_index_name)
    if os.path.exists(main_index_file_path):
        print(f"Removing existing index file: {main_index_file_path}")
        os.remove(main_index_file_path)
    if os.path.exists(METADATA_FILE_PATH): # METADATA_FILE_PATH is global from vector_store
        print(f"Removing existing metadata file: {METADATA_FILE_PATH}")
        os.remove(METADATA_FILE_PATH)

    # 4. Build and save the main index
    print(f"Building and saving FAISS index to '{main_index_name}' and metadata...")
    built_index = build_and_save_index(all_documents_for_index, index_name=main_index_name)

    if built_index:
        print(f"\nSuccessfully built and saved '{main_index_name}' with {built_index.ntotal} entries.")
        print(f"Metadata store saved to '{METADATA_FILE_PATH}'.")
        print("Main RAG index build complete.")
    else:
        print("\nFailed to build the main RAG index.")

if __name__ == "__main__":
    main()
