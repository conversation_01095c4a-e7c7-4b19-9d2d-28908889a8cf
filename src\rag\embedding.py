from sentence_transformers import SentenceTransformer
from typing import List, Union, Optional
import numpy as np

# Load a pre-trained model.
# 'all-mpnet-base-v2' is a good general-purpose model for sentence and paragraph embeddings.
# It's proficient with both text and has seen code during its training.
MODEL_NAME = 'all-MiniLM-L6-v2'
MODEL: Optional[SentenceTransformer] = None

def get_embedding_model() -> SentenceTransformer:
    """
    Loads and returns the SentenceTransformer model.
    Initializes the model on first call.
    """
    global MODEL
    if MODEL is None:
        try:
            MODEL = SentenceTransformer(MODEL_NAME)
            print(f"SentenceTransformer model '{MODEL_NAME}' loaded successfully.")
        except Exception as e:
            print(f"Error loading SentenceTransformer model '{MODEL_NAME}': {e}")
            # Depending on the desired behavior, you might re-raise the exception
            # or handle it by returning None or a dummy model.
            raise
    return MODEL

def get_embeddings(texts: Union[str, List[str]]) -> Optional[np.ndarray]:
    """
    Generates embeddings for the given text(s).

    Args:
        texts (Union[str, List[str]]): A single text string or a list of text strings.

    Returns:
        Optional[np.ndarray]: A numpy array of embeddings, or None if an error occurs.
                              If a single string is input, a 2D array with one row is returned.
                              If a list of strings is input, a 2D array with multiple rows is returned.
    """
    try:
        model = get_embedding_model()
        # The encode method handles both single strings and lists of strings.
        # It returns a numpy array where each row is an embedding.
        embeddings = model.encode(texts, show_progress_bar=False)
        return embeddings
    except Exception as e:
        print(f"Error generating embeddings: {e}")
        return None

if __name__ == "__main__":
    # Example usage:
    sample_texts = [
        "This is an example sentence.",
        "Each sentence is converted to a vector.",
        "Part.makeBox(10, 20, 5)",
        "def create_sphere(radius):\n    return Part.makeSphere(radius)"
    ]
    
    embeddings_array = get_embeddings(sample_texts)
    
    if embeddings_array is not None:
        print(f"\nGenerated embeddings for {len(sample_texts)} texts.")
        print(f"Shape of embeddings array: {embeddings_array.shape}") # (num_texts, embedding_dimension)
        print(f"Embedding for the first text (first 5 dimensions): {embeddings_array[0][:5]}")

    single_text_embedding = get_embeddings("Just one piece of text.")
    if single_text_embedding is not None:
        print(f"\nGenerated embedding for a single text.")
        print(f"Shape of single text embedding array: {single_text_embedding.shape}")
        # If shape is 1D (e.g., (768,)), access directly. Otherwise, access [0].
        if len(single_text_embedding.shape) == 1:
            print(f"Single text embedding (first 5 dimensions): {single_text_embedding[:5]}")
        else:
            print(f"Single text embedding (first 5 dimensions): {single_text_embedding[0][:5]}")

    # Test with chunks from chunking.py (requires chunking.py to be in the same path or PYTHONPATH)
    try:
        from chunking import chunk_guide_en
        guide_chunks_data = chunk_guide_en()
        if guide_chunks_data:
            # Take a small sample of text_content for embedding
            sample_guide_texts = [chunk['text_content'] for chunk in guide_chunks_data[:2]]
            guide_embeddings = get_embeddings(sample_guide_texts)
            if guide_embeddings is not None:
                print(f"\nGenerated embeddings for 2 guide chunks.")
                print(f"Shape of guide_embeddings array: {guide_embeddings.shape}")
    except ImportError:
        print("\nSkipping chunking.py import test as it's not found in the current path.")
    except Exception as e:
        print(f"\nError during test with chunking.py: {e}")
