# -*- coding: utf-8 -*-
import os
import logging
import tempfile
from pathlib import Path
from typing import Tuple, Optional, Any

from openai import OpenAI
from dotenv import load_dotenv
from sqlalchemy.orm import Session # Added import
import uuid # Added for session_id generation
import random # Added for session_id generation

# Attempt to import TextToCADAgent, handle potential ImportError if paths are complex
try:
    from src.core.text_to_cad_agent import TextToCADAgent
    from src.crud.sessions import add_chat_history_entry
    from src.schemas.sessions import ChatRequest as ApiChatRequest # For chat_request_obj
except ImportError:
    # This is a fallback, ideally the path should be correct or PYTHONPATH set up
    # For now, we'll allow PDFProcessor to initialize without it if import fails
    TextToCADAgent = None
    add_chat_history_entry = None
    ApiChatRequest = None
    logging.warning("Could not import TextToCADAgent, add_chat_history_entry, or ApiChatRequest. CAD/DB integration will be affected.")


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("pdf-handler")

class PDFProcessor:
    """
    Handles processing of PDF files using OpenAI's API.

    This class provides functionality to:
    1. Upload PDF files to OpenAI API
    2. Process PDFs with optional custom prompts
    3. Return analysis results
    """

    def __init__(self, api_key: str = None, cad_agent: Optional[Any] = None): # Changed Any to TextToCADAgent if import is reliable
        """
        Initialize the PDF processor with optional API key and CAD agent.

        Args:
            api_key: Optional OpenAI API key. If not provided, will look for
                    OPENAI_API_KEY environment variable.
            cad_agent: Optional instance of TextToCADAgent for CAD generation.
        """
        # Load environment variables from .env file if present
        load_dotenv()

        # Use provided API key or get from environment
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")

        # Initialize OpenAI client
        if self.api_key:
            try:
                self.client = OpenAI(api_key=self.api_key)
                logger.info("OpenAI client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
                self.client = None
        else:
            logger.warning("No OpenAI API key provided")
            self.client = None

        self.cad_agent = cad_agent
        if self.cad_agent and TextToCADAgent and isinstance(self.cad_agent, TextToCADAgent): # Check if TextToCADAgent was imported
            logger.info("TextToCADAgent instance provided to PDFProcessor.")
        elif cad_agent:
            logger.warning("A cad_agent was provided, but TextToCADAgent class was not imported or type mismatch. CAD integration might not work.")
        else:
            logger.info("TextToCADAgent instance not provided to PDFProcessor. Will only perform PDF analysis.")

    def _generate_session_id(self) -> str:
        """Generates a unique session ID."""
        rand_digits = random.randint(100000, 999999)
        rand_uuid_hex = uuid.uuid4().hex[:6]
        return f"session_{rand_uuid_hex}_{rand_digits}"

    def process_pdf(self, db: Session, session_id: str, file_path: str, user_input: str = "") -> Tuple[bool, str]:
        """
        Process a PDF file using OpenAI's API, optionally generate CAD, and save to chat history.

        Args:
            db: SQLAlchemy database session.
            session_id: The session ID for this interaction.
            file_path: Path to the PDF file.
            user_input: Optional user input to send along with the default prompt.

        Returns:
            Tuple of (success: bool, result_message_for_user: str)
        """
        if not self.client:
            return False, "OpenAI client not initialized. Check API key."

        pdf_analysis_text = ""
        cad_agent_output_dict = None
        final_user_message = ""
        file_id = None # Initialize file_id to ensure it's available in finally block if upload succeeds

        try:
            logger.info(f"Processing PDF: {file_path} for session {session_id}")

            default_prompt = "Analyze this PDF and identify if it shows a ['Perforated sheet','Tole','Countersink hole']. Return in this exact format: 'Class: [perforated sheet/tole/countersink hole], Name/code: [value]'. If you can see specific dimensions, hole patterns, or model codes, include them. No other text."
            prompt = default_prompt

            if user_input and user_input.strip():
                prompt = f"{default_prompt} Additional context: {user_input}"

            logger.info(f"Using prompt: {prompt}")

            if not os.path.exists(file_path):
                return False, f"File not found: {file_path}"

            if not file_path.lower().endswith('.pdf'):
                logger.warning(f"File may not be a PDF: {file_path}")

            with open(file_path, "rb") as file_to_upload:
                upload_response = self.client.files.create(
                    file=file_to_upload,
                    purpose="user_data"
                )
            file_id = upload_response.id
            logger.info(f"File uploaded successfully. File ID: {file_id}")

            # Create chat completion with the file
            try:
                completion = self.client.chat.completions.create(
                    model="gpt-4.1-2025-04-14",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "file", "file": {"file_id": file_id}},
                                {"type": "text", "text": prompt}
                            ]
                        }
                    ]
                )
                pdf_analysis_text = completion.choices[0].message.content
                logger.info(f"Successfully processed PDF. Response length: {len(pdf_analysis_text)}")

            except Exception as e_completion:
                logger.error(f"Error during chat completion for PDF in session {session_id}: {e_completion}")
                if add_chat_history_entry and db and session_id:
                    add_chat_history_entry(db, session_id, f"PDF analysis attempt for {file_path}", {"error": str(e_completion), "message": f"Error processing PDF content: {str(e_completion)}"})
                return False, f"Error processing PDF content: {str(e_completion)}"

            # Return only the PDF analysis result - no CAD generation here
            logger.info(f"PDF analysis completed for session {session_id}: {pdf_analysis_text}")
            return True, pdf_analysis_text

        except Exception as e_outer: # Covers file upload errors or other initial errors
            logger.exception(f"Error processing PDF for session {session_id}: {e_outer}")
            if add_chat_history_entry and db and session_id:
                add_chat_history_entry(db, session_id, f"PDF processing attempt for {file_path}", {"error": str(e_outer), "message": f"Error processing PDF: {str(e_outer)}"})
            return False, f"Error processing PDF: {str(e_outer)}"
        finally:
            # Clean up the uploaded file from OpenAI if it was uploaded
            if file_id:
                try:
                    self.client.files.delete(file_id)
                    logger.info(f"Deleted file from OpenAI: {file_id} in finally block")
                except Exception as e_del_finally:
                    logger.warning(f"Failed to delete file from OpenAI in finally block: {e_del_finally}")

    def process_pdf_from_bytes(self, db: Session, session_id: str, file_bytes: bytes, filename: str,
                             user_input: str = "") -> Tuple[bool, str]:
        if not self.client:
            return False, "OpenAI client not initialized. Check API key."

        temp_file = None
        try:
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            temp_file = tempfile.NamedTemporaryFile(delete=False, dir=temp_dir, suffix=".pdf", prefix=f"{filename.replace(' ', '_')}_")
            temp_file.write(file_bytes)
            temp_file.close() # Close the file so it can be opened by process_pdf

            success, analysis_or_cad_result = self.process_pdf(db, session_id, temp_file.name, user_input)
            return success, analysis_or_cad_result

        except Exception as e:
            logger.exception(f"Error processing PDF from bytes for session {session_id}: {e}")
            return False, f"Error processing PDF from bytes: {str(e)}"
        finally:
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                    logger.info(f"Deleted temporary file: {temp_file.name}")
                except Exception as e_unlink:
                    logger.warning(f"Failed to delete temporary file: {e_unlink}")

    async def process_uploaded_file(self, db: Session, uploaded_file, user_input: str = "", session_id: Optional[str] = None) -> Tuple[bool, str, Optional[str]]:
        """
        Process an uploaded PDF file from FastAPI's UploadFile and save to chat history.

        Args:
            db: SQLAlchemy database session.
            uploaded_file: The UploadFile object from FastAPI.
            user_input: Optional user input to send along with the default prompt.
            session_id: Optional session ID. If not provided, will generate a new one.

        Returns:
            Tuple of (success: bool, result_message_for_user: str, session_id: Optional[str])
        """
        # Use provided session_id or generate a new one
        if not session_id:
            session_id = self._generate_session_id()
            logger.info(f"Generated new session ID {session_id} for uploaded file {uploaded_file.filename}")
        else:
            logger.info(f"Using provided session ID {session_id} for uploaded file {uploaded_file.filename}")

        if not self.client:
            return False, "OpenAI client not initialized. Check API key.", session_id

        temp_file_path_obj = None # Initialize to ensure it's defined for finally block
        try:
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            filename = uploaded_file.filename.replace(" ", "_").replace("/", "_")
            temp_file_path_obj = temp_dir / filename # Use Path object

            content = await uploaded_file.read()
            with open(temp_file_path_obj, "wb") as buffer:
                buffer.write(content)

            logger.info(f"Saved uploaded file to: {temp_file_path_obj}")

            success, analysis_or_cad_result = self.process_pdf(db, session_id, str(temp_file_path_obj), user_input)
            return success, analysis_or_cad_result, session_id

        except Exception as e:
            logger.exception(f"Error processing uploaded file: {e}")
            return False, f"Error processing uploaded file: {str(e)}", session_id
        finally:
            if temp_file_path_obj and temp_file_path_obj.exists():
                try:
                    os.unlink(temp_file_path_obj)
                    logger.info(f"Deleted temporary file: {temp_file_path_obj}")
                except Exception as e_unlink_uploaded:
                    logger.warning(f"Failed to delete temporary uploaded file: {e_unlink_uploaded}")

# Usage example:
# processor = PDFProcessor()
# success, response = processor.process_pdf("path/to/your.pdf", "Analyze this document")
# if success:
#     print(f"Response: {response}")
# else:
#     print(f"Error: {response}")
