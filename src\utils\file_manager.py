"""
File Manager Module

This module provides utility functions for managing files in the dfm-ShapeChatBot project.
It handles file operations like saving, loading, and converting files.
"""

import os
import json
import shutil
import logging
import subprocess
from pathlib import Path
from typing import Optional, Union, List, Dict, Any, Tuple

from src.utils.path_manager import (
    get_output_path, 
    get_unique_filepath,
    sanitize_filename,
    PROJECT_ROOT
)

# Configure logging
logger = logging.getLogger(__name__)

def save_code_file(
    code: str,
    shape_type: str,
    dimensions: Union[str, Dict[str, float]],
    design_requirements: Optional[Dict] = None
) -> Path:
    """
    Save generated Python code to a file.
    
    Args:
        code: The Python code to save
        shape_type: The type of shape
        dimensions: Key dimensions
        design_requirements: Optional design requirements
        
    Returns:
        Path to the saved file
    """
    # Get output path
    filepath = get_output_path(shape_type, dimensions, "py", design_requirements)
    
    # Ensure filepath is unique
    filepath = get_unique_filepath(filepath)
    
    # Save the file
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(code)
        logger.info(f"Saved code file to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Error saving code file: {e}")
        raise

def save_metadata_file(
    metadata: Dict,
    shape_type: str,
    dimensions: Union[str, Dict[str, float]],
    design_requirements: Optional[Dict] = None
) -> Path:
    """
    Save metadata to a JSON file.
    
    Args:
        metadata: The metadata to save
        shape_type: The type of shape
        dimensions: Key dimensions
        design_requirements: Optional design requirements
        
    Returns:
        Path to the saved file
    """
    # Get output path
    filepath = get_output_path(shape_type, dimensions, "json", design_requirements)
    
    # Ensure filepath is unique
    filepath = get_unique_filepath(filepath)
    
    # Save the file
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved metadata file to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Error saving metadata file: {e}")
        raise

def execute_freecad_script(script_path: Path) -> Tuple[bool, str]:
    """
    Execute a FreeCAD script.
    
    Args:
        script_path: Path to the script
        
    Returns:
        Tuple of (success, message)
    """
    try:
        # Execute FreeCAD command
        result = subprocess.run(
            ["freecadcmd", str(script_path)],
            capture_output=True,
            text=True,
            check=False
        )
        
        # Check if execution was successful
        if result.returncode == 0:
            logger.info(f"Successfully executed FreeCAD script: {script_path}")
            return True, result.stdout
        else:
            logger.error(f"Error executing FreeCAD script: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        logger.error(f"Exception executing FreeCAD script: {e}")
        return False, str(e)

def convert_obj_to_gltf(obj_path: Path) -> Tuple[bool, Optional[Path]]:
    """
    Convert an OBJ file to GLTF format.
    
    Args:
        obj_path: Path to the OBJ file
        
    Returns:
        Tuple of (success, gltf_path)
    """
    # Determine GLTF output path
    gltf_path = obj_path.parent.parent.parent / "gltf" / obj_path.parent.name / f"{obj_path.stem}.gltf"
    
    # Ensure output directory exists
    gltf_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Ensure filepath is unique
    gltf_path = get_unique_filepath(gltf_path)
    
    try:
        # Set execution policy for PowerShell
        policy_cmd = 'powershell -Command "Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned -Force"'
        subprocess.run(policy_cmd, shell=True, check=True, capture_output=True, text=True)
        
        # Run obj2gltf
        cmd = f'obj2gltf -i "{obj_path}" -o "{gltf_path}"'
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        
        logger.info(f"Successfully converted OBJ to GLTF: {gltf_path}")
        return True, gltf_path
    except subprocess.CalledProcessError as e:
        logger.error(f"Error during conversion: {e}")
        return False, None
    except Exception as e:
        logger.error(f"Unexpected error during conversion: {e}")
        return False, None

def find_output_files(
    shape_type: str,
    dimensions: Union[str, Dict[str, float]],
    output_types: List[str] = ["step", "obj", "gltf", "py", "json"]
) -> Dict[str, Optional[Path]]:
    """
    Find output files for a specific shape.
    
    Args:
        shape_type: The type of shape
        dimensions: Key dimensions
        output_types: Types of output files to find
        
    Returns:
        Dictionary mapping output types to file paths
    """
    result = {}
    
    for output_type in output_types:
        # Get base directory for this output type
        base_dir = get_output_path(shape_type, dimensions, output_type).parent.parent
        
        # Get sanitized shape type and dimensions for pattern matching
        sanitized_shape = sanitize_filename(shape_type)
        sanitized_dims = sanitize_filename(str(dimensions))
        
        # Pattern to match
        pattern = f"{sanitized_shape}_{sanitized_dims}_*.{output_type}"
        
        # Search for matching files in all date directories
        matching_files = []
        for date_dir in base_dir.iterdir():
            if date_dir.is_dir():
                matching_files.extend(date_dir.glob(pattern))
        
        # Sort by modification time (newest first)
        matching_files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
        
        # Get the newest file if any
        result[output_type] = matching_files[0] if matching_files else None
    
    return result

def move_legacy_outputs():
    """
    Move legacy output files from the old location to the new structure.
    """
    # Old output directory
    old_output_dir = PROJECT_ROOT.parent / "cad_outputs_generated"
    
    if not old_output_dir.exists():
        logger.info("No legacy output directory found.")
        return
    
    logger.info(f"Moving legacy outputs from {old_output_dir}")
    
    # Create a directory for today
    today = get_output_path("legacy", "migration", "step").parent
    
    # Process each file in the old directory
    for file_path in old_output_dir.iterdir():
        if not file_path.is_file():
            continue
        
        # Determine file type from extension
        extension = file_path.suffix.lower()[1:]  # Remove the dot
        
        if extension in ["py"]:
            dest_dir = today.parent.parent / "code" / today.name
        elif extension in ["json"]:
            dest_dir = today.parent.parent / "metadata" / today.name
        elif extension in ["step"]:
            dest_dir = today.parent.parent / "cad" / today.name
        elif extension in ["obj"]:
            dest_dir = today.parent.parent / "obj" / today.name
        elif extension in ["gltf"]:
            dest_dir = today.parent.parent / "gltf" / today.name
        else:
            # Skip unknown file types
            logger.warning(f"Skipping unknown file type: {file_path}")
            continue
        
        # Ensure destination directory exists
        dest_dir.mkdir(parents=True, exist_ok=True)
        
        # Destination path
        dest_path = dest_dir / file_path.name
        
        # Ensure destination path is unique
        dest_path = get_unique_filepath(dest_path)
        
        try:
            # Copy the file
            shutil.copy2(file_path, dest_path)
            logger.info(f"Copied {file_path} to {dest_path}")
        except Exception as e:
            logger.error(f"Error copying {file_path}: {e}")
    
    logger.info("Legacy output migration completed.")
