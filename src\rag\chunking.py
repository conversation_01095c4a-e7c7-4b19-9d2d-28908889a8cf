import re
import json
from pathlib import Path

def chunk_guide_en(file_path: str = "data/guide_en.txt") -> list[dict[str, str]]:
    """
    Chunks the guide_en.txt file by individual command descriptions.

    Each command description (signature and example) is treated as a chunk.
    """
    chunks = []
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return chunks

    # Regex to find command blocks starting with "- `Command.name(...)"
    # and including the following "*Example: ..." line.
    # It handles potential variations in spacing and newlines between command and example.
    command_pattern = re.compile(
        r"^- `([a-zA-Z0-9_]+\.[a-zA-Z0-9_]+[^`]+)`\s*-\s*(.*?)\n\s*\*Example:\s*(.*)",
        re.MULTILINE
    )

    # Simpler pattern for lines that might not have a separate example line but are part of a list
    # or for entries that are more descriptive text under a sub-heading.
    # This is a fallback and might need refinement based on actual file structure nuances.
    # For now, focusing on the primary command structure.

    current_workbench = ""
    current_category = ""

    lines = content.splitlines()
    buffer = []
    
    for i, line in enumerate(lines):
        if line.startswith("**") and line.endswith("**"): # Workbench title
            current_workbench = line.strip("* ")
            current_category = "" # Reset category when workbench changes
        elif line.startswith("*") and not line.startswith("*Example:") and not line.startswith("**") and ":" in line and not line.startswith("- `"): # Category title like *Basic Objects:*
            current_category = line.strip("* :")
        elif line.startswith("- `"): # Start of a command description
            if buffer: # Process previous buffered command
                # This logic might be too simple if commands span more than 2 lines before example
                # For now, assuming command definition is one line, example is the next relevant one.
                command_line = ""
                example_line = ""
                description = ""
                
                # Attempt to extract command and its immediate description
                match_command_desc = re.match(r"^- `(.*?)`\s*-\s*(.*)", buffer[0])
                if match_command_desc:
                    command_signature = match_command_desc.group(1)
                    description = match_command_desc.group(2)

                # Find the example line
                for buf_line in buffer:
                    if buf_line.strip().startswith("*Example:"):
                        example_line = buf_line.strip().replace("*Example:", "").strip()
                        # Clean up potential extra markdown
                        if example_line.startswith("* `") and example_line.endswith("`"):
                            example_line = example_line[3:-1]
                        elif example_line.startswith("`") and example_line.endswith("`"):
                            example_line = example_line[1:-1]
                        break
                
                if command_signature and example_line:
                    chunks.append({
                        "workbench": current_workbench,
                        "category": current_category,
                        "command_signature": command_signature,
                        "description": description,
                        "example": example_line,
                        "source": file_path,
                        "text_content": f"- `{command_signature}` - {description}\n  *Example:* {example_line}"
                    })
                buffer = [] # Clear buffer for next command
            buffer.append(line)
        elif buffer and line.strip().startswith("*Example:"): # Example line for current command in buffer
            buffer.append(line)
            # Now process the completed command block
            command_signature = ""
            description = ""
            example_code = ""

            # Extract command signature and description from the first line in buffer
            match_command_desc = re.match(r"^- `(.*?)`\s*-\s*(.*)", buffer[0])
            if match_command_desc:
                command_signature = match_command_desc.group(1).strip()
                description = match_command_desc.group(2).strip()
            
            # Extract example code from the last line in buffer
            match_example = re.match(r"\s*\*Example:\s*(.*)", buffer[-1])
            if match_example:
                example_code = match_example.group(1).strip()
                # Clean up potential extra markdown
                if example_code.startswith("* `") and example_code.endswith("`"):
                    example_code = example_code[3:-1]
                elif example_code.startswith("`") and example_code.endswith("`"):
                    example_code = example_code[1:-1]


            if command_signature: # Ensure we have at least a command
                text_content = f"- `{command_signature}` - {description}"
                if example_code:
                    text_content += f"\n  *Example:* {example_code}"
                
                chunks.append({
                    "workbench": current_workbench,
                    "category": current_category,
                    "command_signature": command_signature,
                    "description": description,
                    "example": example_code,
                    "source": file_path,
                    "text_content": text_content
                })
            buffer = [] # Reset buffer
        elif buffer: # Continuation of a command description or other text
             # If it's not a new command or an example, append to buffer if buffer is active
            buffer.append(line)

    # Process any remaining command in buffer after loop (e.g., last command in file)
    if buffer:
        command_signature = ""
        description = ""
        example_code = ""

        match_command_desc = re.match(r"^- `(.*?)`\s*-\s*(.*)", buffer[0])
        if match_command_desc:
            command_signature = match_command_desc.group(1).strip()
            description = match_command_desc.group(2).strip()
        
        for buf_line in buffer:
            if buf_line.strip().startswith("*Example:"):
                match_example = re.match(r"\s*\*Example:\s*(.*)", buf_line)
                if match_example:
                    example_code = match_example.group(1).strip()
                    # Clean up potential extra markdown
                    if example_code.startswith("* `") and example_code.endswith("`"):
                        example_code = example_code[3:-1]
                    elif example_code.startswith("`") and example_code.endswith("`"):
                        example_code = example_code[1:-1]
                break
        
        if command_signature:
            text_content = f"- `{command_signature}` - {description}"
            if example_code:
                text_content += f"\n  *Example:* {example_code}"
            chunks.append({
                "workbench": current_workbench,
                "category": current_category,
                "command_signature": command_signature,
                "description": description,
                "example": example_code,
                "source": file_path,
                "text_content": text_content
            })

    return chunks


def chunk_example_txt(file_path: str = "data/example.txt") -> list[dict[str, str]]:
    """
    Chunks the example.txt file by individual script examples.

    Each script, identified by a line starting with '#example', is a chunk.
    """
    chunks = []
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return chunks

    # Split the content by the delimiter '#example'
    # The first part of the split will be empty if the file starts with the delimiter,
    # or it will be content before the first delimiter (which we can ignore if it's not an example).
    raw_scripts = content.split("#example ")
    
    for script_block in raw_scripts:
        if not script_block.strip():
            continue

        lines = script_block.splitlines()
        script_name = lines[0].strip()
        script_content = "\n".join(lines[1:]).strip()
        
        if script_content: # Ensure there's actual script content
            chunks.append({
                "script_name": script_name,
                "script_content": script_content,
                "source": file_path
            })
            
    return chunks

def chunk_chat_history(file_path: str = "data/chat_history1.json") -> list[dict]:
    """
    Chunks the chat_history.json file.

    Each 'generated_code' is a document, with 'user_message', 
    'is_edit_request', and 'timestamp' as metadata.
    """
    chunks = []
    import json
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            history_data = json.load(f)
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return chunks
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {file_path}")
        return chunks

    for entry in history_data:
        if "generated_code" in entry and "user_message" in entry:
            chunks.append({
                "code_content": entry["generated_code"],
                "user_message": entry["user_message"],
                "is_edit_request": entry.get("is_edit_request", False), # Default to False if missing
                "timestamp": entry.get("timestamp", ""), # Default to empty string if missing
                "source": file_path
            })
    return chunks

def chunk_class_rules(class_name: str) -> list[dict[str, str]]:
    """
    Chunks the rules.json file for a specific class.
    Each rule becomes a separate chunk for RAG indexing.
    """
    chunks = []
    rules_path = Path(f"data/Class/{class_name}/rules.json")
    
    if not rules_path.exists():
        print(f"No rules file found for class: {class_name}")
        return chunks
        
    try:
        with open(rules_path, "r", encoding="utf-8") as f:
            rules_data = json.load(f)
            
        class_name = rules_data.get("class_name", class_name)
        rules = rules_data.get("rules", [])
        
        for rule in rules:
            chunk = {
                "rule_id": rule.get("id", ""),
                "class_name": class_name,
                "category": rule.get("category", ""),
                "title": rule.get("title", ""),
                "description": rule.get("description", ""),
                "rule": rule.get("rule", ""),
                "severity": rule.get("severity", "info"),
                "validation_code": rule.get("validation_code", ""),
                "error_message": rule.get("error_message", ""),
                "parameters": rule.get("parameters", []),
                "source": f"data/Class/{class_name}/rules.json",
                "text_content": f"Rule {rule.get('id', '')}: {rule.get('title', '')}\n"
                              f"Class: {class_name}\n"
                              f"Category: {rule.get('category', '')}\n"
                              f"Description: {rule.get('description', '')}\n"
                              f"Rule: {rule.get('rule', '')}\n"
                              f"Severity: {rule.get('severity', '')}\n"
                              f"Validation: {rule.get('validation_code', '')}\n"
                              f"Error Message: {rule.get('error_message', '')}\n"
                              f"Parameters: {', '.join(rule.get('parameters', []))}"
            }
            chunks.append(chunk)
            
    except Exception as e:
        print(f"Error loading rules for {class_name}: {e}")
        
    return chunks

def chunk_all_class_rules() -> list[dict[str, str]]:
    """
    Chunks rules from all available classes.
    """
    all_chunks = []
    class_dir = Path("data/Class")
    
    if not class_dir.exists():
        return all_chunks
        
    for class_path in class_dir.iterdir():
        if class_path.is_dir():
            class_name = class_path.name
            class_chunks = chunk_class_rules(class_name)
            all_chunks.extend(class_chunks)
            print(f"Loaded {len(class_chunks)} rules for class: {class_name}")
            
    return all_chunks

if __name__ == "__main__":
    # Example usage for guide_en.txt:
    guide_chunks = chunk_guide_en()
    if guide_chunks:
        print(f"Successfully chunked 'data/guide_en.txt' into {len(guide_chunks)} chunks.")
        # for i, chunk in enumerate(guide_chunks[:3]): # Print first 3 chunks
        #     print(f"\nChunk {i+1}:")
        #     print(f"  Workbench: {chunk['workbench']}")
        #     print(f"  Category: {chunk['category']}")
        #     print(f"  Command: {chunk['command_signature']}")
        #     print(f"  Description: {chunk['description']}")
        #     print(f"  Example: {chunk['example']}")
        #     print(f"  Text Content:\n{chunk['text_content']}")
        
        # Test specific known command
        for chunk in guide_chunks:
            if "Part.makeBox" in chunk["command_signature"]:
                print("\nFound Part.makeBox:")
                print(f"  Workbench: {chunk['workbench']}")
                print(f"  Category: {chunk['category']}")
                print(f"  Command: {chunk['command_signature']}")
                print(f"  Description: {chunk['description']}")
                print(f"  Example: {chunk['example']}")
                print(f"  Text Content:\n{chunk['text_content']}")
                break
    else:
        print("No chunks were created from 'data/guide_en.txt'.")

    # Example usage for example.txt:
    example_chunks = chunk_example_txt()
    if example_chunks:
        print(f"\nSuccessfully chunked 'data/example.txt' into {len(example_chunks)} chunks.")
        # print("\nFirst example chunk:")
        # print(f"  Name: {example_chunks[0]['script_name']}")
        # print(f"  Content snippet:\n{example_chunks[0]['script_content'][:200]}...") # Print first 200 chars
    else:
        print("No chunks were created from 'data/example.txt'.")

    # Example usage for chat_history.json:
    chat_chunks = chunk_chat_history()
    if chat_chunks:
        print(f"\nSuccessfully chunked 'data/chat_history.json' into {len(chat_chunks)} chunks.")
        # if chat_chunks:
        #     print("\nFirst chat history chunk:")
        #     print(f"  User Message: {chat_chunks[0]['user_message']}")
        #     print(f"  Is Edit: {chat_chunks[0]['is_edit_request']}")
        #     print(f"  Timestamp: {chat_chunks[0]['timestamp']}")
        #     print(f"  Code Content snippet:\n{chat_chunks[0]['code_content'][:200]}...")
    else:
        print("No chunks were created from 'data/chat_history.json'.")

    # Example usage for class rules:
    rules_chunks = chunk_all_class_rules()
    if rules_chunks:
        print(f"\nSuccessfully chunked class rules into {len(rules_chunks)} chunks.")
        # Print first rule chunk as example
        if rules_chunks:
            print("\nFirst rule chunk:")
            print(f"  Rule ID: {rules_chunks[0]['rule_id']}")
            print(f"  Class: {rules_chunks[0]['class_name']}")
            print(f"  Title: {rules_chunks[0]['title']}")
            print(f"  Severity: {rules_chunks[0]['severity']}")
    else:
        print("No chunks were created from class rules.")
