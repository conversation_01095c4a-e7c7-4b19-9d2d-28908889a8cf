<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Text-to-CAD Chatbot</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- Syntax highlighting -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <!-- Three.js for 3D rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <link rel="stylesheet" href="/static/css/styles.css" />
    <style>
      /* Chat bubble styles */
      .chat-bubble {
        max-width: 80%;
        word-wrap: break-word;
        animation: slideIn 0.3s ease-out;
      }

      .chat-bubble.user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-left: auto;
        border-radius: 18px 18px 4px 18px;
      }

      .chat-bubble.bot {
        background: #f8fafc;
        color: #374151;
        border: 1px solid #e5e7eb;
        margin-right: auto;
        border-radius: 18px 18px 18px 4px;
      }

      .chat-bubble.system {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #92400e;
        margin: 0 auto;
        border-radius: 18px;
        text-align: center;
        font-size: 0.875rem;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Typing indicator */
      .typing-indicator {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 18px 18px 18px 4px;
        margin-right: auto;
        max-width: 80px;
      }

      .typing-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #9ca3af;
        margin: 0 2px;
        animation: typing 1.4s infinite ease-in-out;
      }

      .typing-dot:nth-child(1) {
        animation-delay: 0s;
      }
      .typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }
      .typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes typing {
        0%,
        60%,
        100% {
          transform: translateY(0);
          opacity: 0.4;
        }
        30% {
          transform: translateY(-10px);
          opacity: 1;
        }
      }

      /* Chat input styles */
      .chat-input-container {
        background: white;
        border-radius: 25px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
      }

      .chat-input-container:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .chat-input {
        border: none;
        outline: none;
        resize: none;
        background: transparent;
      }

      /* Progress animations */
      .progress-step {
        transition: all 0.3s ease;
      }

      .progress-step.active {
        background: #eff6ff;
        border-color: #3b82f6;
      }

      .progress-step.completed {
        background: #f0fdf4;
        border-color: #22c55e;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .chat-bubble {
          max-width: 90%;
        }

        .container {
          padding: 1rem;
        }
      }

      /* File upload area */
      .file-drop-zone {
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .file-drop-zone.drag-over {
        border-color: #667eea;
        background: #eff6ff;
      }

      /* Loading states */
      .btn-loading {
        position: relative;
        color: transparent !important;
      }

      .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: button-spin 0.8s linear infinite;
      }

      @keyframes button-spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* 3D Viewer styles */
      .viewer-3d {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
      }

      .viewer-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        max-width: 180px;
        justify-content: flex-end;
      }

      .viewer-control-btn.select-face-active {
        background: rgba(34, 197, 94, 0.9) !important;
        color: white !important;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
      }

      .viewer-control-btn {
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        color: #374151;
      }

      .viewer-control-btn:hover {
        background: white;
        transform: scale(1.05);
      }

      .viewer-info {
        position: absolute;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 100;
        max-width: 250px;
        width: auto;
      }

      .viewer-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: white;
        text-align: center;
      }

      .viewer-placeholder i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.7;
      }

      .viewer-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        text-align: center;
        z-index: 200;
      }

      .viewer-loading .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Main Chat Interface -->
    <div class="container mx-auto px-4 py-6 max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="flex items-center justify-center mb-4">
          <div
            class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4"
          >
            <i class="fas fa-robot text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-800">
              Text-to-CAD Assistant
            </h1>
            <p class="text-gray-600">
              Generate 3D models from your descriptions
            </p>
          </div>
        </div>

        <!-- Status Bar -->
        <div
          class="flex items-center justify-center space-x-6 text-sm text-gray-500"
        >
          <div class="flex items-center">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span>Online</span>
          </div>
          <div id="session-indicator" class="hidden flex items-center">
            <i class="fas fa-link mr-2"></i>
            <span
              >Session: <span id="session-id-display" class="font-mono"></span
            ></span>
          </div>
          <div
            id="edit-mode-indicator"
            class="hidden flex items-center text-purple-600"
          >
            <i class="fas fa-pencil-alt mr-2"></i>
            <span>Edit Mode Active</span>
          </div>
        </div>
      </div>

      <!-- Main Chat Container -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Chat Area -->
        <div class="lg:col-span-2">
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden h-[600px] flex flex-col"
          >
            <!-- Chat Header -->
            <div
              class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-comments mr-3"></i>
                  <div>
                    <h3 class="font-semibold">CAD Assistant</h3>
                    <p class="text-blue-100 text-sm">
                      Ready to help you create 3D models
                    </p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <label class="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      id="edit-mode-toggle"
                      class="sr-only peer"
                    />
                    <div
                      class="relative w-11 h-6 bg-blue-400 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"
                    ></div>
                    <span class="ml-2 text-sm text-blue-100">Edit Mode</span>
                  </label>
                  <button
                    id="refresh-btn"
                    class="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    title="New Chat"
                  >
                    <i class="fas fa-plus"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Chat Messages -->
            <div
              id="chat-messages"
              class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
            >
              <!-- Welcome Message -->
              <div class="flex justify-center">
                <div class="chat-bubble system p-3 max-w-md">
                  <i class="fas fa-sparkles mr-2"></i>
                  Welcome! Describe a 3D model you'd like to create, or upload
                  an image/PDF for reference.
                </div>
              </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 bg-white border-t border-gray-200">
              <!-- File Upload Area -->
              <div id="selected-file-info" class="mb-3 empty:hidden"></div>

              <!-- Input Form -->
              <form
                id="chat-form"
                class="chat-input-container flex items-end p-2"
              >
                <div class="flex-1 relative">
                  <textarea
                    id="user-input"
                    placeholder="Describe your CAD model or ask a question..."
                    class="chat-input w-full p-3 text-sm max-h-32 min-h-[44px] resize-none"
                    rows="1"
                  ></textarea>
                </div>
                <div class="flex items-center space-x-2 ml-2">
                  <button
                    type="button"
                    id="attach-file-btn"
                    class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Attach file"
                  >
                    <i class="fas fa-paperclip"></i>
                  </button>
                  <button
                    type="submit"
                    id="send-btn"
                    class="p-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                  >
                    <i class="fas fa-paper-plane"></i>
                  </button>
                </div>
              </form>

              <!-- Input Hints -->
              <div
                class="flex items-center justify-between text-xs text-gray-500 mt-2"
              >
                <div class="flex items-center space-x-4">
                  <span
                    ><i class="fas fa-keyboard mr-1"></i>Type description</span
                  >
                  <span
                    ><i class="fas fa-image mr-1"></i>Paste image (Ctrl+V)</span
                  >
                  <span><i class="fas fa-file mr-1"></i>Drag & drop files</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span id="char-count">0</span>
                  <span>•</span>
                  <span>JPG, PNG, PDF (max 20MB)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Progress Panel -->
          <div
            id="processing-progress-section"
            class="bg-white rounded-2xl shadow-lg overflow-hidden"
          >
            <div
              class="bg-gradient-to-r from-green-500 to-teal-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-cogs mr-3"></i>
                  <div>
                    <h3 class="font-semibold">Processing</h3>
                    <p class="text-green-100 text-sm">
                      CAD generation progress
                    </p>
                  </div>
                </div>
                <div
                  id="overall-progress-percentage"
                  class="text-2xl font-bold"
                >
                  0%
                </div>
              </div>
            </div>

            <div class="p-4 space-y-4">
              <!-- Progress Steps -->
              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-analysis"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-analysis"
                  >
                    <i class="fas fa-search text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Analysis</p>
                    <p class="text-xs text-gray-500" id="step-status-analysis">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-parameters"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-parameters"
                  >
                    <i class="fas fa-list-alt text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Parameters</p>
                    <p
                      class="text-xs text-gray-500"
                      id="step-status-parameters"
                    >
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-generation_code"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-generation_code"
                  >
                    <i class="fas fa-code text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Generation</p>
                    <p
                      class="text-xs text-gray-500"
                      id="step-status-generation_code"
                    >
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-export"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-export"
                  >
                    <i class="fas fa-file-export text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Export</p>
                    <p class="text-xs text-gray-500" id="step-status-export">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <div
                class="progress-step border border-gray-200 rounded-lg p-3"
                id="step-complete"
              >
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3"
                    id="step-icon-complete"
                  >
                    <i class="fas fa-check-circle text-gray-500"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Complete</p>
                    <p class="text-xs text-gray-500" id="step-status-complete">
                      Waiting...
                    </p>
                  </div>
                </div>
              </div>

              <!-- Overall Progress Bar -->
              <div class="mt-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Overall Progress</span>
                  <span id="overall-progress-text">0 of 5 steps</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    id="overall-progress-bar"
                    class="bg-gradient-to-r from-green-500 to-teal-600 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 3D Model Viewer Panel -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div
              class="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-4"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-cube mr-3"></i>
                  <div>
                    <h3 class="font-semibold">3D Model Viewer</h3>
                    <p class="text-purple-100 text-sm">Interactive 3D preview</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  
                  <button
                    id="fullscreen-viewer-btn"
                    class="hidden px-3 py-1 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors text-sm"
                    title="Fullscreen"
                  >
                    <i class="fas fa-expand mr-1"></i>Fullscreen
                  </button>
                  <button
                    id="view-step-btn"
                    class="hidden px-3 py-1 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors text-sm"
                  >
                    <i class="fas fa-external-link-alt mr-1"></i>STEP Viewer
                  </button>
                </div>
              </div>
            </div>

            <div class="p-4">
              <!-- 3D Viewer Container -->
              <div id="viewer-3d-container" class="h-80 mb-4">
                <div class="viewer-3d">
                  <div class="viewer-placeholder">
                    <i class="fas fa-cube"></i>
                    <p class="text-sm">3D model will appear here</p>
                    <p class="text-xs opacity-75 mt-1">Generate a model to see the preview</p>
                  </div>

                  <!-- Loading indicator -->
                  <div id="viewer-loading" class="viewer-loading hidden">
                    <div class="spinner"></div>
                    <p class="text-sm">Loading 3D model...</p>
                  </div>

                  <!-- Viewer controls -->
                  <div id="viewer-controls" class="viewer-controls hidden">
                    <button class="viewer-control-btn" id="reset-camera-btn" title="Reset Camera">
                      <i class="fas fa-home"></i>
                    </button>
                    <button class="viewer-control-btn" id="wireframe-toggle-btn" title="Toggle Wireframe">
                      <i class="fas fa-project-diagram"></i>
                    </button>
                    <button class="viewer-control-btn" id="download-obj-btn" title="Download OBJ">
                      <i class="fas fa-download"></i>
                    </button>
                    <button class="viewer-control-btn hidden" id="select-face-btn" title="Select Face to Chat">
                      <i class="fas fa-mouse-pointer"></i>
                    </button>
                    <button class="viewer-control-btn hidden" id="select-edge-btn" title="Select Edge to Chat">
                      <i class="fas fa-bezier-curve"></i>
                    </button>
                  </div>

                  <!-- Model info -->
                  <div id="viewer-info" class="viewer-info hidden">
                    <div id="model-info-text">No model loaded</div>
                  </div>
                </div>
              </div>

              <!-- 3D Viewer Debug Tools (initially hidden) -->
              <div id="viewer-debug-panel" class="border border-gray-200 rounded-lg mb-4 hidden">
                <div class="p-4">
                  <h3 class="text-lg font-semibold mb-3">3D Viewer Debug Tools</h3>
                  
                  <!-- Debug Tools -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 class="font-medium mb-2">1. Kiểm tra thư viện</h4>
                      <div class="grid grid-cols-2 gap-2 mb-4">
                        <button id="check-three" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 text-sm">
                          Kiểm tra THREE.js
                        </button>
                        <button id="check-model-viewer" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 text-sm">
                          Kiểm tra ModelViewer
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <h4 class="font-medium mb-2">2. Kiểm tra API</h4>
                      <div class="mb-3">
                        <input type="text" id="obj-path" class="w-full p-2 border rounded text-sm" 
                               value="outputs/obj/2025-05-30/box_20250530164735.obj" 
                               placeholder="Đường dẫn tới file OBJ">
                      </div>
                      <div class="grid grid-cols-2 gap-2">
                        <button id="check-file" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 text-sm">
                          Kiểm tra file tồn tại
                        </button>
                        <button id="load-model" class="bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600 text-sm">
                          Tải model
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- URL Testing Section -->
                  <div class="border-t border-gray-200 pt-4 mt-4">
                    <h4 class="font-medium mb-2">3. URL Analysis</h4>
                    <div class="mb-3">
                      <input type="text" id="test-url" class="w-full p-2 border rounded text-sm" 
                             value="/api/3d-viewer/outputs/obj/2025-05-30/box_20250530164735.obj" 
                             placeholder="Enter URL to test">
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-2 mb-4">
                      <button id="analyze-url" class="bg-yellow-500 text-white py-2 px-4 rounded hover:bg-yellow-600 text-sm">
                        Analyze URL
                      </button>
                      <button id="load-url-directly" class="bg-indigo-500 text-white py-2 px-4 rounded hover:bg-indigo-600 text-sm">
                        Load URL Directly
                      </button>
                      <button id="test-api-get" class="bg-cyan-500 text-white py-2 px-4 rounded hover:bg-cyan-600 text-sm">
                        Test GET
                      </button>
                      <button id="test-api-head" class="bg-cyan-500 text-white py-2 px-4 rounded hover:bg-cyan-600 text-sm">
                        Test HEAD
                      </button>
                      <button id="test-download-api" class="bg-cyan-500 text-white py-2 px-4 rounded hover:bg-cyan-600 text-sm">
                        Test Download API
                      </button>
                    </div>
                  </div>
                  
                  <!-- Debug Log -->
                  <div class="mt-4">
                    <h4 class="font-medium mb-2">Debug Log</h4>
                    <div id="debug-log" class="h-48 bg-gray-900 text-green-400 p-4 rounded-lg overflow-y-auto font-mono text-xs">
                      <!-- Logs will appear here -->
                    </div>
                  </div>
                  
                  <!-- URL Analysis Result -->
                  <div id="url-analysis" class="mt-4 p-3 bg-gray-100 rounded-lg hidden">
                    <h4 class="font-semibold text-gray-700 mb-2">URL Analysis Result</h4>
                    <div id="url-analysis-content" class="text-sm"></div>
                  </div>
                </div>
              </div>

              <!-- Code Output (collapsible) -->
              <div class="border border-gray-200 rounded-lg">
                <button
                  id="toggle-code-btn"
                  class="w-full p-3 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
                >
                  <span class="text-sm font-medium text-gray-700">
                    <i class="fas fa-code mr-2"></i>Generated Code
                  </span>
                  <i class="fas fa-chevron-down text-gray-400"></i>
                </button>
                <div id="code-output-container" class="hidden">
                  <div
                    id="code-output"
                    class="bg-gray-50 p-4 h-48 overflow-y-auto text-sm border-t border-gray-200"
                  >
                    <div
                      class="flex flex-col items-center justify-center h-full text-gray-400"
                    >
                      <i class="fas fa-code text-2xl mb-2"></i>
                      <p class="text-center text-sm">
                        Generated code will appear here
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200">
              <h3 class="font-semibold text-gray-800 flex items-center">
                <i class="fas fa-bolt mr-2 text-yellow-500"></i>
                Quick Actions
              </h3>
            </div>
            <div class="p-4 space-y-3">
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="box"
              >
                <div class="flex items-center">
                  <i class="fas fa-cube text-blue-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Box</p>
                    <p class="text-xs text-gray-500">Simple rectangular box</p>
                  </div>
                </div>
              </button>
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="cylinder"
              >
                <div class="flex items-center">
                  <i class="fas fa-circle text-green-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Cylinder</p>
                    <p class="text-xs text-gray-500">Cylindrical shape</p>
                  </div>
                </div>
              </button>
              <button
                class="quick-action-btn w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                data-action="cone"
              >
                <div class="flex items-center">
                  <i class="fas fa-play text-purple-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium">Create Cone</p>
                    <p class="text-xs text-gray-500">Conical shape</p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Chatbot Toggle (for smaller screens) -->
    <div id="mobile-chat-toggle" class="lg:hidden fixed bottom-6 right-6 z-50">
      <button
        class="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg flex items-center justify-center hover:shadow-xl transition-all"
      >
        <i class="fas fa-robot text-xl"></i>
      </button>
    </div>

    <!-- Hidden elements for compatibility -->
    <div id="chat-toggle" class="hidden"></div>
    <div id="chatbot-widget" class="hidden"></div>
    <div id="chat-history" class="hidden"></div>

    <script src="/static/js/3d-viewer.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
      // Debug panel functionality
      document.addEventListener('DOMContentLoaded', () => {
        const debugPanel = document.getElementById('viewer-debug-panel');
        const toggleDebugBtn = document.getElementById('toggle-debug-btn');
        const debugLog = document.getElementById('debug-log');
        const objPathInput = document.getElementById('obj-path');
        const testUrlInput = document.getElementById('test-url');
        const urlAnalysisDiv = document.getElementById('url-analysis');
        const urlAnalysisContent = document.getElementById('url-analysis-content');
        
        // Toggle debug panel
        if (toggleDebugBtn && debugPanel) {
          toggleDebugBtn.addEventListener('click', () => {
            debugPanel.classList.toggle('hidden');
            
            // Log initial message when panel is opened
            if (!debugPanel.classList.contains('hidden')) {
              addDebugLog('Debug panel opened', 'info');
            }
          });
        }
        
        // Add log entry to debug log
        function addDebugLog(message, type = 'info') {
          if (!debugLog) return;
          
          const colors = {
            info: 'text-blue-400',
            success: 'text-green-400',
            warning: 'text-yellow-400',
            error: 'text-red-400'
          };
          const time = new Date().toLocaleTimeString();
          const entry = document.createElement('div');
          entry.className = colors[type] || colors.info;
          entry.innerHTML = `[${time}] ${message}`;
          debugLog.appendChild(entry);
          debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // Check THREE.js
        document.getElementById('check-three')?.addEventListener('click', () => {
          try {
            if (typeof THREE === 'undefined') {
              addDebugLog('THREE is not defined!', 'error');
            } else {
              addDebugLog(`THREE.js loaded. Version: ${THREE.REVISION}`, 'success');
              // Check required components
              if (typeof THREE.OBJLoader === 'undefined') {
                addDebugLog('THREE.OBJLoader is not available', 'warning');
              }
              if (typeof THREE.OrbitControls === 'undefined') {
                addDebugLog('THREE.OrbitControls is not available', 'warning');
              }
            }
          } catch (error) {
            addDebugLog(`Error checking THREE: ${error.message}`, 'error');
          }
        });
        
        // Check ModelViewer
        document.getElementById('check-model-viewer')?.addEventListener('click', () => {
          try {
            if (typeof ModelViewer3D === 'undefined') {
              addDebugLog('ModelViewer3D is not defined!', 'error');
            } else {
              addDebugLog('ModelViewer3D class is available', 'success');
              
              // Check if modelViewer already exists globally
              if (window.modelViewer) {
                addDebugLog('ModelViewer instance already exists in window', 'success');
              } else {
                // Try to create instance
                try {
                  window.modelViewer = new ModelViewer3D('viewer-3d-container');
                  addDebugLog('ModelViewer3D initialized successfully', 'success');
                } catch (e) {
                  addDebugLog(`Failed to initialize ModelViewer3D: ${e.message}`, 'error');
                }
              }
            }
          } catch (error) {
            addDebugLog(`Error checking ModelViewer: ${error.message}`, 'error');
          }
        });
        
        // Check if file exists
        document.getElementById('check-file')?.addEventListener('click', async () => {
          if (!objPathInput) return;
          
          const objPath = objPathInput.value.trim();
          if (!objPath) {
            addDebugLog('Please enter a file path', 'warning');
            return;
          }
          
          addDebugLog(`Checking file: ${objPath}`, 'info');
          
          // Prepare URL - ensure proper format
          let url = objPath;
          if (!url.startsWith('/api/')) {
            url = `/api/3d-viewer/${url}`;
          }
          
          try {
            addDebugLog(`Sending HEAD request to: ${url}`, 'info');
            const response = await fetch(url, { 
              method: 'HEAD',
              headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
              }
            });
            
            addDebugLog(`Response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            // Log response headers
            const headers = {};
            response.headers.forEach((value, key) => {
              headers[key] = value;
            });
            addDebugLog(`Response headers: ${JSON.stringify(headers, null, 2)}`, 'info');
            
            if (response.ok) {
              addDebugLog(`File exists: ${url}`, 'success');
            } else {
              addDebugLog(`File not found: ${url} (${response.status} - ${response.statusText})`, 'error');
            }
          } catch (error) {
            addDebugLog(`Network error: ${error.message}`, 'error');
            console.error('Fetch error details:', error);
          }
        });
        
        // Load model with enhanced error handling
        document.getElementById('load-model')?.addEventListener('click', async () => {
          if (!objPathInput) return;
          
          const objPath = objPathInput.value.trim();
          if (!objPath) {
            addDebugLog('Please enter a file path', 'warning');
            return;
          }
          
          if (!window.modelViewer) {
            addDebugLog('ModelViewer not initialized. Initializing now...', 'warning');
            try {
              window.modelViewer = new ModelViewer3D('viewer-3d-container');
            } catch (e) {
              addDebugLog(`Failed to initialize ModelViewer: ${e.message}`, 'error');
              return;
            }
          }
          
          // Extract filename from path
          const fileName = objPath.split('/').pop();
          
          addDebugLog(`Loading model: ${objPath}`, 'info');
          
          // Try a GET request first to diagnose issues
          try {
            addDebugLog(`Testing GET request to: ${objPath}`, 'info');
            let testUrl = objPath;
            if (!testUrl.startsWith('/api/')) {
              testUrl = `/api/3d-viewer/${testUrl}`;
            }
            
            const response = await fetch(testUrl, { 
              method: 'GET',
              headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
              }
            });
            
            addDebugLog(`GET test response: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            if (!response.ok) {
              // Try fallback with a different URL format
              const fallbackUrl = `/api/3d-viewer/outputs/obj/2025-05-30/${fileName}`;
              addDebugLog(`Trying fallback URL: ${fallbackUrl}`, 'warning');
              
              const fallbackResponse = await fetch(fallbackUrl, { method: 'GET' });
              addDebugLog(`Fallback response: ${fallbackResponse.status} ${fallbackResponse.statusText}`, 
                        fallbackResponse.ok ? 'success' : 'error');
                        
              if (fallbackResponse.ok) {
                // Update path to use fallback
                objPath = fallbackUrl;
              }
            }
          } catch (error) {
            addDebugLog(`GET test error: ${error.message}`, 'error');
          }
          
          // Now try loading the model
          try {
            addDebugLog(`Attempting to load model with path: ${objPath}`, 'info');
            const success = await window.modelViewer.loadModel(objPath, fileName);
            if (success) {
              addDebugLog(`Model loaded successfully: ${fileName}`, 'success');
            } else {
              addDebugLog('Model failed to load', 'error');
            }
          } catch (error) {
            addDebugLog(`Error loading model: ${error.message}`, 'error');
            console.error('Load model error details:', error);
          }
        });
        
        // URL analysis
        document.getElementById('analyze-url')?.addEventListener('click', () => {
          if (!testUrlInput || !urlAnalysisDiv || !urlAnalysisContent) return;
          
          const url = testUrlInput.value.trim();
          if (!url) {
            addDebugLog('Please enter a URL to analyze', 'warning');
            return;
          }
          
          addDebugLog(`Analyzing URL: ${url}`, 'info');
          
          try {
            // Parse URL
            const parser = document.createElement('a');
            parser.href = url.startsWith('http') ? url : window.location.origin + url;
            
            // Get URL parts
            const protocol = parser.protocol;
            const host = parser.host;
            const path = parser.pathname;
            const queryString = parser.search;
            const hash = parser.hash;
            
            // Break down path parts
            const pathParts = path.split('/').filter(part => part);
            const extension = path.split('.').pop();
            
            // Analyze possible issues
            const issues = [];
            if (path.includes('//')) issues.push('URL contains double slashes');
            if (!extension) issues.push('URL does not have a file extension');
            if (extension && !['obj', 'mtl', 'gltf', 'glb', 'stl'].includes(extension.toLowerCase())) {
              issues.push('File extension is not a common 3D format');
            }
            
            // Suggested fixes
            const suggestions = [];
            if (issues.length > 0) {
              if (path.includes('//')) {
                suggestions.push('Remove double slashes: ' + path.replace(/\/+/g, '/'));
              }
              
              // Suggest direct download URL
              if (path.includes('api/3d-viewer')) {
                const downloadPath = path.replace('/api/3d-viewer', '/download');
                suggestions.push(`Try download endpoint: ${downloadPath}`);
              }
            }
            
            // Create analysis result
            const analysis = {
              original: url,
              absoluteUrl: parser.href,
              protocol,
              host,
              path,
              queryString,
              hash,
              pathParts,
              extension,
              issues,
              suggestions
            };
            
            // Display analysis
            urlAnalysisDiv.classList.remove('hidden');
            urlAnalysisContent.innerHTML = `
              <div class="grid grid-cols-1 gap-2">
                <div><strong>Original:</strong> ${analysis.original}</div>
                <div><strong>Absolute URL:</strong> ${analysis.absoluteUrl}</div>
                <div><strong>Protocol:</strong> ${analysis.protocol}</div>
                <div><strong>Host:</strong> ${analysis.host}</div>
                <div><strong>Path:</strong> ${analysis.path}</div>
                <div><strong>Path Parts:</strong> ${analysis.pathParts.join(' / ')}</div>
                <div><strong>Extension:</strong> ${analysis.extension || 'None'}</div>
                
                ${analysis.issues.length > 0 ? `
                  <div class="mt-2">
                    <strong class="text-red-500">Issues (${analysis.issues.length}):</strong>
                    <ul class="list-disc ml-4 text-red-500">
                      ${analysis.issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                  </div>
                ` : '<div class="text-green-500 mt-2"><strong>No issues detected</strong></div>'}
                
                ${analysis.suggestions.length > 0 ? `
                  <div class="mt-2">
                    <strong class="text-blue-500">Suggestions:</strong>
                    <ul class="list-disc ml-4 text-blue-500">
                      ${analysis.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                  </div>
                ` : ''}
              </div>
            `;
            
            addDebugLog('URL analysis complete', 'success');
          } catch (error) {
            addDebugLog(`Error analyzing URL: ${error.message}`, 'error');
            console.error('URL analysis error:', error);
          }
        });
        
        // Load URL directly
        document.getElementById('load-url-directly')?.addEventListener('click', () => {
          if (!testUrlInput) return;
          
          const url = testUrlInput.value.trim();
          if (!url) {
            addDebugLog('Please enter a URL to load', 'warning');
            return;
          }
          
          addDebugLog(`Opening URL directly: ${url}`, 'info');
          
          // Open URL in new tab
          window.open(url, '_blank');
        });
        
        // Test API with GET
        document.getElementById('test-api-get')?.addEventListener('click', async () => {
          if (!testUrlInput) return;
          
          const url = testUrlInput.value.trim();
          if (!url) {
            addDebugLog('Please enter a URL to test', 'warning');
            return;
          }
          
          addDebugLog(`Testing GET request to: ${url}`, 'info');
          
          try {
            const response = await fetch(url, { 
              method: 'GET',
              headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
              }
            });
            
            addDebugLog(`GET response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            // Log response headers
            const headers = {};
            response.headers.forEach((value, key) => {
              headers[key] = value;
            });
            addDebugLog(`Response headers: ${JSON.stringify(headers, null, 2)}`, 'info');
            
            // Try to get response body for text files
            const contentType = response.headers.get('content-type');
            if (response.ok && contentType && (contentType.includes('text') || contentType.includes('json'))) {
              const body = await response.text();
              addDebugLog(`Response body (first 100 chars): ${body.substring(0, 100)}...`, 'info');
            }
          } catch (error) {
            addDebugLog(`GET request error: ${error.message}`, 'error');
            console.error('GET error details:', error);
          }
        });
        
        // Test API with HEAD
        document.getElementById('test-api-head')?.addEventListener('click', async () => {
          if (!testUrlInput) return;
          
          const url = testUrlInput.value.trim();
          if (!url) {
            addDebugLog('Please enter a URL to test', 'warning');
            return;
          }
          
          addDebugLog(`Testing HEAD request to: ${url}`, 'info');
          
          try {
            const response = await fetch(url, { 
              method: 'HEAD',
              headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
              }
            });
            
            addDebugLog(`HEAD response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            // Log response headers
            const headers = {};
            response.headers.forEach((value, key) => {
              headers[key] = value;
            });
            addDebugLog(`Response headers: ${JSON.stringify(headers, null, 2)}`, 'info');
          } catch (error) {
            addDebugLog(`HEAD request error: ${error.message}`, 'error');
            console.error('HEAD error details:', error);
          }
        });
        
        // Test download API
        document.getElementById('test-download-api')?.addEventListener('click', async () => {
          if (!testUrlInput) return;
          
          const url = testUrlInput.value.trim();
          if (!url) {
            addDebugLog('Please enter a URL to test', 'warning');
            return;
          }
          
          // Convert 3d-viewer URL to download URL
          let downloadUrl = url;
          if (url.includes('/api/3d-viewer/')) {
            downloadUrl = url.replace('/api/3d-viewer/', '/download/');
          }
          
          addDebugLog(`Testing download URL: ${downloadUrl}`, 'info');
          
          try {
            const response = await fetch(downloadUrl, { 
              method: 'GET',
              headers: {
                'Accept': '*/*',
                'Cache-Control': 'no-cache'
              }
            });
            
            addDebugLog(`Download response status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
            
            // Log response headers
            const headers = {};
            response.headers.forEach((value, key) => {
              headers[key] = value;
            });
            addDebugLog(`Response headers: ${JSON.stringify(headers, null, 2)}`, 'info');
            
            if (response.ok) {
              addDebugLog('Download URL works! File can be downloaded.', 'success');
              // Open download in new tab
              window.open(downloadUrl, '_blank');
            }
          } catch (error) {
            addDebugLog(`Download request error: ${error.message}`, 'error');
            console.error('Download error details:', error);
          }
        });
      });
    </script>
  </body>
</html>
