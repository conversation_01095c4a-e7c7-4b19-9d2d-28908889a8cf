# Template for checking if there's missing information in the user request
parameter_check_template = """You are a CAD expert, your task is to determine if the user's description contains enough information to create a 3D model.
1. THE SHORTEST RESPONSE POSSIBLE IS PREFERRED.
2. EACH question MUST be 4 words or less.

Example questions:
- length x width = ?
- thickness = ?


User request:
{user_text}

Analyzed information:
```json
{design_requirements}
```

Check if any information is missing. Missing information could be:
1. Specific dimensions of shapes (length, width, height, radius, etc.)
2. Relative positions of shapes
3. Direction or rotation angles of shapes
4. Information about boolean operations (cut, union, intersection)

Return JSON in the following format:
```json
{{
  "missing_info": true/false,
  "questions": ["Question 1 to get missing information", "Question 2", ...],
  "explanation": "Brief explanation about missing information"
}}
```

If no information is missing, set "missing_info" to false and return an empty "questions" array.
If information is missing, set "missing_info" to true and list specific questions to get the missing information.
Questions should be clear, specific and easy to understand.

IMPORTANT GUIDELINES:
1. THE SHORTEST RESPONSE POSSIBLE IS PREFERRED.
2. EACH question MUST be 4 words or less. Break complex questions into multiple short questions.
3. If the user's message contains phrases like "dont ask anymore", "no more questions", "stop asking", 
   "proceed anyway", or similar expressions indicating they don't want further questions, 
   ALWAYS set "missing_info" to false regardless of any actual missing information.
"""

# Pydantic model for parameter check results
class ParameterCheckResult(BaseModel):
    missing_info: bool = Field(description="Whether there is missing information")
    questions: List[str] = Field(description="List of questions to get missing information")
    explanation: Optional[str] = Field(None, description="Explanation about missing information")
