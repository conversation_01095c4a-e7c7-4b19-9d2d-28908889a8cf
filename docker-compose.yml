version: '3.8'

volumes:
  tolery_mysql_data:

services:
  db:
    image: mysql:8.0.33
    restart: always
    container_name: tolery_chatbot_db
    env_file: .env
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    command: ['mysqld', '--default-authentication-plugin=mysql_native_password']
    ports:
      - "3306:3306"
    volumes:
      - tolery_mysql_data:/var/lib/mysql