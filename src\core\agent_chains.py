import json
import re
from langchain_core.runnables import <PERSON>nableLambda, RunnableParallel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from .templates import (
    unified_analysis_and_parameter_check_template,
    code_generation_template,
    code_editing_template
)
from .agent_utils import (
    parse_unified_analysis, clean_code, format_retrieved_context
)


def _format_previous_requirements_for_guidance(latest_requirements):
    """Format previous requirements as helpful guidance context"""
    if not latest_requirements:
        return ""
    
    try:
        guidance_parts = []
        
        # Add basic design info
        if hasattr(latest_requirements, 'title') and latest_requirements.title:
            guidance_parts.append(f"Current design: {latest_requirements.title}")
        
        # Add shape information with key dimensions
        if hasattr(latest_requirements, 'shapes') and latest_requirements.shapes:
            for i, shape in enumerate(latest_requirements.shapes):
                if hasattr(shape, 'shape_type') and hasattr(shape, 'dimensions'):
                    shape_info = f"Shape {i+1}: {shape.shape_type}"
                    if shape.dimensions:
                        dims = []
                        for key, value in shape.dimensions.items():
                            if value is not None:
                                dims.append(f"{key}={value}")
                        if dims:
                            shape_info += f" ({', '.join(dims)})"
                    guidance_parts.append(shape_info)
        
        # Add operation information
        if hasattr(latest_requirements, 'operations') and latest_requirements.operations:
            for i, op in enumerate(latest_requirements.operations):
                if hasattr(op, 'operation_type'):
                    op_info = f"Operation {i+1}: {op.operation_type}"
                    if hasattr(op, 'base_shape') and op.base_shape:
                        op_info += f" (base: {op.base_shape}"
                        if hasattr(op, 'tool_shape') and op.tool_shape:
                            op_info += f", tool: {op.tool_shape}"
                        op_info += ")"
                    guidance_parts.append(op_info)
        
        # Add current status information
        if hasattr(latest_requirements, 'missing_info') and latest_requirements.missing_info:
            if hasattr(latest_requirements, 'questions') and latest_requirements.questions:
                guidance_parts.append(f"Previously asked: {', '.join(latest_requirements.questions)}")
        
        # Add any comments with warnings
        if hasattr(latest_requirements, 'comments') and latest_requirements.comments:
            if "RULE VIOLATION WARNING" in latest_requirements.comments:
                guidance_parts.append(f"Previous warnings: {latest_requirements.comments}")
        
        if guidance_parts:
            return "PREVIOUS ANALYSIS CONTEXT:\n" + "\n".join(f"- {part}" for part in guidance_parts) + "\n\nPlease consider this context when analyzing the current user input. If the user is providing corrections or additional parameters, update the design accordingly."
        
    except Exception as e:
        # Fallback to string representation if there's any issue
        return f"Previous requirements context: {str(latest_requirements)}"
    
    return ""


def create_unified_processing_chain(expert_llm, local_retriever=None):
    """Create the unified processing chain for analysis and parameter checking"""
    
    unified_analysis_prompt = ChatPromptTemplate.from_template(unified_analysis_and_parameter_check_template)
    
    chain = RunnableLambda(
        lambda x: {
            "user_text": x["user_text"],
            "previous_responses": x["previous_responses_formatted"],
            "dynamic_guidance": _format_previous_requirements_for_guidance(x["latest_requirements_for_guidance"]),
            "retrieved_context": format_retrieved_context(
                local_retriever(x["user_text"]) if local_retriever else []
            )
        }
    ) | RunnableParallel(
        {
            "unified_analysis": unified_analysis_prompt | expert_llm | StrOutputParser(),
            "template_inputs": lambda x: x
        }
    ) | RunnableLambda(
        lambda x: {
            "unified_output_obj": parse_unified_analysis(x["unified_analysis"]),
            "raw_unified_json": x["unified_analysis"],
            "retrieved_context_for_code_gen": x["template_inputs"]["retrieved_context"]
        }
    )
    
    return chain


def create_code_generation_chain(advanced_llm):
    """Create the RAG code generation chain"""
    
    code_generation_prompt = ChatPromptTemplate.from_template(code_generation_template)
    
    chain = (
        RunnableLambda(
            lambda x: {
                "design_requirements": json.dumps({
                    "title": x["design_requirements_obj"].title,
                    "shapes": [s.dict() for s in x["design_requirements_obj"].shapes] if x["design_requirements_obj"].shapes else [],
                    "operations": [o.dict() for o in x["design_requirements_obj"].operations] if x["design_requirements_obj"].operations else None,
                    "comments": x["design_requirements_obj"].comments,
                    "complexity_level": x["design_requirements_obj"].complexity_level,
                }, indent=2),
                "retrieved_context": x["retrieved_context"],
                "sanitized_title": (
                    lambda dr_obj: (
                        ''.join(c for c in re.sub(r'[^\w\s-]', '', dr_obj.title).strip() if ord(c) < 128).replace(' ', '_')
                        or "generated_cad"
                    )
                )(x["design_requirements_obj"])
            }
        )
        | code_generation_prompt
        | advanced_llm
        | StrOutputParser()
        | RunnableLambda(clean_code)
    )
    
    return chain


def create_code_editing_chain(expert_llm):
    """Create the code editing chain"""
    
    code_editing_prompt = ChatPromptTemplate.from_template(code_editing_template)
    
    chain = (
        {
            "original_code": lambda x: x["original_code"],
            "user_request": lambda x: x["user_request"],
            "retrieved_context": lambda x: x["retrieved_context"],
            "sanitized_title": lambda x: x["sanitized_title"]
        }
        | code_editing_prompt
        | expert_llm
        | StrOutputParser()
        | RunnableLambda(clean_code)
    )
    
    return chain 