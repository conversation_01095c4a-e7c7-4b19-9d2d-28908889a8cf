/**
 * 3D Model Viewer using Three.js
 * Handles loading and displaying OBJ files in the web interface
 * Fixed to rotate around model center like CAD software
 */

class ModelViewer3D {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentModel = null;
        this.isWireframe = false;
        this.modelCenter = new THREE.Vector3(0, 0, 0);
        this.modelBoundingBox = null;
        this.selectedMesh = null;
        this.originalColor = null;
        this.originalModelSize = null; // Store original model size before scaling
        this.modelScaleFactor = 1; // Store the scale factor applied
        this.selectedFaceInfo = null; // Store information about the currently selected face
        this.isSelectFaceMode = false; // Track if we're in face selection mode
        this.isSelectEdgeMode = false; // Track if we're in edge selection mode
        this.highlightEdgeMesh = null; // Store the highlighted edge mesh
        this.selectedEdgeInfo = null; // Store information about selected edge
        this.originalSourceUrl = null; // Store the original source URL of the loaded model
        this.modelInfo = {
            vertices: 0,
            faces: 0,
            fileName: ''
        };
        this.controlsInfo = {
            left: "Left click: Rotate/Orbit",
            wheel: "Wheel: Zoom in/out",
            middle: "Middle click or Shift+Left: Pan view"
        };

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            45,
            this.container.clientWidth / this.container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(5, 5, 5);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Add renderer to container
        const viewerElement = this.container.querySelector('.viewer-3d');
        viewerElement.appendChild(this.renderer.domElement);

        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.1;
        this.controls.rotateSpeed = 0.7;
        this.controls.panSpeed = 0.7;
        this.controls.zoomSpeed = 1.2;
        this.controls.screenSpacePanning = true;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;

        // CAD-like controls configuration
        this.controls.mouseButtons = {
            LEFT: THREE.MOUSE.ROTATE,       // Left click: rotate/orbit
            MIDDLE: THREE.MOUSE.PAN,        // Middle click: pan
            RIGHT: THREE.MOUSE.PAN          // Right click: pan (alternative)
        };

        // Enable Shift + Left click for panning (common CAD behavior)
        this.controls.keyPanSpeed = 10.0;
        this.controls.keys = {
            LEFT: "ArrowLeft",
            UP: "ArrowUp",
            RIGHT: "ArrowRight",
            BOTTOM: "ArrowDown"
        };

        // Add lights
        this.setupLighting();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupLighting() {

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.65);
        this.scene.add(ambientLight);

        const directionalLight1 = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight1.position.set(10, 10, 10);
        directionalLight1.castShadow = true;
        directionalLight1.shadow.mapSize.width = 2048;
        directionalLight1.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight1);

        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.35);
        directionalLight2.position.set(-10, 5, -5);
        this.scene.add(directionalLight2);

        const directionalLight3 = new THREE.DirectionalLight(0xffffff, 0.25);
        directionalLight3.position.set(5, -10, -7);
        this.scene.add(directionalLight3);
    }

    setupEventListeners() {
        // Reset camera button
        document.getElementById('reset-camera-btn')?.addEventListener('click', () => {
            this.resetCamera();
        });

        // Wireframe toggle button
        document.getElementById('wireframe-toggle-btn')?.addEventListener('click', () => {
            this.toggleWireframe();
        });

        // Download OBJ button
        document.getElementById('download-obj-btn')?.addEventListener('click', () => {
            this.downloadCurrentModel();
        });

        // Fullscreen button
        document.getElementById('fullscreen-viewer-btn')?.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Code toggle button
        document.getElementById('toggle-code-btn')?.addEventListener('click', () => {
            this.toggleCodeOutput();
        });

        // Select Face button
        document.getElementById('select-face-btn')?.addEventListener('click', () => {
            this.toggleSelectFaceMode();
        });
        
        // Select Edge button
        document.getElementById('select-edge-btn')?.addEventListener('click', () => {
            this.toggleSelectEdgeMode();
        });

        const viewerElement = this.container.querySelector('.viewer-3d');
        if (viewerElement) {
            viewerElement.addEventListener('pointerdown', this.onModelClick.bind(this));
        }
        
        
    }

    async loadOBJFile(objUrl, fileName = '') {
        try {
            // Store the original source URL
            this.originalSourceUrl = objUrl;
            
            this.showLoading(true);
            this.hideControls();

            // Remove previous model
            if (this.currentModel) {
                this.scene.remove(this.currentModel);
            }

            // Validate URL
            if (!objUrl) {
                console.error('[3D-VIEWER] Error: Empty OBJ URL provided');
                this.hideLoading();
                this.showError('Invalid OBJ URL');
                return null;
            }
            
            // Prepare URL - make sure it doesn't have double slashes
            let finalUrl = objUrl;
            if (finalUrl.startsWith('/api/3d-viewer/')) {
                // URL is already properly formatted
            } else if (finalUrl.startsWith('/')) {
                finalUrl = `/api/3d-viewer${finalUrl}`;
            } else {
                finalUrl = `/api/3d-viewer/${finalUrl}`;
            }
            
            console.log(`[3D-VIEWER] Final URL for loading: ${finalUrl}`);

            // Try fetching the file first to check if it exists
            let headRequestSucceeded = false;
            
            try {
                console.log(`[3D-VIEWER] Checking if file exists with HEAD request: ${finalUrl}`);
                const headResponse = await fetch(finalUrl, { 
                    method: 'HEAD',
                    headers: {
                        'Accept': '*/*',
                        'Cache-Control': 'no-cache'
                    }
                });
                console.log(`[3D-VIEWER] HEAD response status: ${headResponse.status} ${headResponse.statusText}`);
                
                if (headResponse.ok) {
                    console.log('[3D-VIEWER] HEAD request succeeded');
                    headRequestSucceeded = true;
                } else if (headResponse.status === 405) {
                    // 405 Method Not Allowed - server doesn't allow HEAD requests
                    console.warn('[3D-VIEWER] HEAD method not allowed, will try GET directly');
                } else {
                    console.error(`[3D-VIEWER] File not found: ${finalUrl}, status: ${headResponse.status}`);
                }
            } catch (headError) {
                console.warn(`[3D-VIEWER] HEAD request failed: ${headError.message}`);
            }
            
            // If HEAD failed, try a GET request to check if file exists
            if (!headRequestSucceeded) {
                try {
                    console.log(`[3D-VIEWER] Trying GET request: ${finalUrl}`);
                    const getResponse = await fetch(finalUrl, { 
                        method: 'GET',
                        headers: {
                            'Accept': '*/*',
                            'Cache-Control': 'no-cache'
                        }
                    });
                    console.log(`[3D-VIEWER] GET response status: ${getResponse.status} ${getResponse.statusText}`);
                    
                    if (!getResponse.ok) {
                        // Try common fallback paths
                        if (fileName) {
                            console.log('[3D-VIEWER] Trying common fallback paths');
                            const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '-');
                            const fallbackPaths = [
                                `/api/3d-viewer/outputs/obj/${todayDate}/${fileName}`,
                                `/api/3d-viewer/outputs/obj/latest/${fileName}`,
                                `/api/3d-viewer/download/outputs/obj/${todayDate}/${fileName}`
                            ];
                            
                            for (const fallbackPath of fallbackPaths) {
                                console.log(`[3D-VIEWER] Trying fallback path: ${fallbackPath}`);
                                const fallbackResponse = await fetch(fallbackPath, { method: 'GET' });
                                if (fallbackResponse.ok) {
                                    console.log(`[3D-VIEWER] Fallback path successful: ${fallbackPath}`);
                                    finalUrl = fallbackPath;
                                    break;
                                }
                            }
                        }
                        
                        if (finalUrl === objUrl) {
                            // No fallback worked, show error
                            this.hideLoading();
                            this.showError(`File not found: ${getResponse.status} - ${getResponse.statusText}`);
                            return null;
                        }
                    }
                } catch (getError) {
                    console.error(`[3D-VIEWER] GET error: ${getError.message}`);
                    this.hideLoading();
                    this.showError(`Network error: ${getError.message}`);
                    return null;
                }
            }

            // Load OBJ file
            const loader = new THREE.OBJLoader();
            console.log(`[3D-VIEWER] Starting OBJ load with THREE.OBJLoader: ${finalUrl}`);

            return new Promise((resolve, reject) => {
                loader.load(
                    finalUrl,
                    (object) => {
                        console.log(`[3D-VIEWER] OBJ loaded successfully from: ${finalUrl}`);
                        this.currentModel = object;

                        // Apply default material
                        object.traverse((child) => {
                            if (child.isMesh) {
                                child.material = new THREE.MeshLambertMaterial({
                                    color: 0x888888,
                                    side: THREE.DoubleSide
                                });
                                child.castShadow = true;
                                child.receiveShadow = true;
                            }
                        });

                        // Add to scene
                        this.scene.add(object);

                        // Center and scale the model - FIXED VERSION
                        this.centerAndScaleModel(object);

                        // Update model info
                        this.updateModelInfo(object, fileName);

                        // Show controls and info
                        this.showControls();
                        this.showInfo();
                        this.hideLoading();

                        // Update control info with correct orbit center
                        this.updateControlsInfoDisplay();

                        console.log('[3D-VIEWER] Model fully processed and added to scene');
                        resolve(object);
                    },
                    (progress) => {
                        const percent = progress.loaded / progress.total * 100;
                        console.log(`[3D-VIEWER] Loading progress: ${percent.toFixed(1)}%`);
                    },
                    (error) => {
                        console.error('[3D-VIEWER] Error loading OBJ file:', error);
                        this.hideLoading();
                        this.showError(`Failed to load 3D model: ${error.message || 'Unknown error'}`);
                        reject(error);
                    }
                );
            });
        } catch (error) {
            console.error('[3D-VIEWER] Error in loadOBJFile:', error);
            this.hideLoading();
            this.showError(`Error: ${error.message || 'Unknown error'}`);
            throw error;
        }
    }

    centerAndScaleModel(object) {
        // Calculate bounding box BEFORE any transformations
        const box = new THREE.Box3().setFromObject(object);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // Store the ORIGINAL model size and center for later calculations
        this.originalModelSize = size.clone();
        this.modelCenter = center.clone();
        this.modelBoundingBox = box.clone();

        // Move model so its center is at origin
        object.position.sub(center);

        // Adjust Y position so model sits on the grid
        const minY = box.min.y - center.y; // Relative to new position
        if (minY < 0) {
            object.position.y -= minY - 0.01; // Lift slightly above
            // Update model center to reflect this adjustment
            this.modelCenter.y += (-minY + 0.01);
        }

        // Scale the model to fit nicely in view
        const maxDim = Math.max(size.x, size.y, size.z);
        const scale = 5 / maxDim;
        this.modelScaleFactor = scale; // Store the scale factor
        object.scale.setScalar(scale);

        // Scale the model center accordingly
        this.modelCenter.multiplyScalar(scale);

        // After positioning and scaling, the visual center is at (0, y_offset, 0)
        // Set orbit controls to rotate around the visual center of the scaled model
        const visualCenter = new THREE.Vector3(0, object.position.y + (size.y * scale) / 2, 0);
        this.controls.target.copy(visualCenter);

        // Position camera for good initial view
        this.resetCamera();

        // Force controls update
        this.controls.update();
    }

    updateModelInfo(object, fileName) {
        let vertices = 0;
        let triangleFaces = 0;
        let actualFaces = 0;

        object.traverse((child) => {
            if (child.isMesh && child.geometry) {
                if (child.geometry.attributes.position) {
                    vertices += child.geometry.attributes.position.count;
                }

                // Count triangular faces
                let childTriangleFaces = 0;
                if (child.geometry.index) {
                    childTriangleFaces = child.geometry.index.count / 3;
                } else if (child.geometry.attributes.position) {
                    childTriangleFaces = child.geometry.attributes.position.count / 3;
                }
                triangleFaces += childTriangleFaces;

                // Calculate actual faces by grouping coplanar triangles
                actualFaces += this.calculateActualFaces(child.geometry);
            }
        });

        this.modelInfo = {
            vertices: Math.floor(vertices),
            faces: Math.floor(triangleFaces), // Keep triangle count for reference
            actualFaces: Math.floor(actualFaces), // Real face count
            fileName: fileName || 'model.obj'
        };

        this.updateInfoDisplay();
    }

    calculateActualFaces(geometry) {
        if (!geometry.attributes.position) return 0;

        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;

        // Get all triangles
        const triangles = [];
        const triangleCount = indices ? indices.length / 3 : positions.length / 9;

        for (let i = 0; i < triangleCount; i++) {
            let v1, v2, v3;

            if (indices) {
                const i1 = indices[i * 3] * 3;
                const i2 = indices[i * 3 + 1] * 3;
                const i3 = indices[i * 3 + 2] * 3;

                v1 = new THREE.Vector3(positions[i1], positions[i1 + 1], positions[i1 + 2]);
                v2 = new THREE.Vector3(positions[i2], positions[i2 + 1], positions[i2 + 2]);
                v3 = new THREE.Vector3(positions[i3], positions[i3 + 1], positions[i3 + 2]);
            } else {
                const base = i * 9;
                v1 = new THREE.Vector3(positions[base], positions[base + 1], positions[base + 2]);
                v2 = new THREE.Vector3(positions[base + 3], positions[base + 4], positions[base + 5]);
                v3 = new THREE.Vector3(positions[base + 6], positions[base + 7], positions[base + 8]);
            }

            // Calculate triangle normal
            const edge1 = new THREE.Vector3().subVectors(v2, v1);
            const edge2 = new THREE.Vector3().subVectors(v3, v1);
            const normal = new THREE.Vector3().crossVectors(edge1, edge2).normalize();

            // Calculate triangle center
            const center = new THREE.Vector3()
                .addVectors(v1, v2)
                .add(v3)
                .divideScalar(3);

            triangles.push({
                vertices: [v1, v2, v3],
                normal: normal,
                center: center,
                used: false
            });
        }

        // Group coplanar triangles into faces
        let faceCount = 0;
        const tolerance = 0.01; // Tolerance for normal comparison
        const distanceTolerance = 0.01; // Tolerance for plane distance

        for (let i = 0; i < triangles.length; i++) {
            if (triangles[i].used) continue;

            const currentTriangle = triangles[i];
            triangles[i].used = true;
            faceCount++;

            // Find all triangles that are coplanar with this one
            for (let j = i + 1; j < triangles.length; j++) {
                if (triangles[j].used) continue;

                const otherTriangle = triangles[j];

                // Check if normals are similar (coplanar)
                const normalDot = Math.abs(currentTriangle.normal.dot(otherTriangle.normal));
                if (normalDot > (1 - tolerance)) {
                    // Check if triangles are on the same plane
                    const centerDiff = new THREE.Vector3().subVectors(otherTriangle.center, currentTriangle.center);
                    const distanceToPlane = Math.abs(centerDiff.dot(currentTriangle.normal));

                    if (distanceToPlane < distanceTolerance) {
                        triangles[j].used = true;
                    }
                }
            }
        }

        return faceCount;
    }

    findActualFace(geometry, clickedTriangleIndex) {
        if (!geometry.attributes.position) return null;

        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;

        // Get all triangles with their info
        const triangles = [];
        const triangleCount = indices ? indices.length / 3 : positions.length / 9;

        for (let i = 0; i < triangleCount; i++) {
            let v1, v2, v3;

            if (indices) {
                const i1 = indices[i * 3] * 3;
                const i2 = indices[i * 3 + 1] * 3;
                const i3 = indices[i * 3 + 2] * 3;

                v1 = new THREE.Vector3(positions[i1], positions[i1 + 1], positions[i1 + 2]);
                v2 = new THREE.Vector3(positions[i2], positions[i2 + 1], positions[i2 + 2]);
                v3 = new THREE.Vector3(positions[i3], positions[i3 + 1], positions[i3 + 2]);
            } else {
                const base = i * 9;
                v1 = new THREE.Vector3(positions[base], positions[base + 1], positions[base + 2]);
                v2 = new THREE.Vector3(positions[base + 3], positions[base + 4], positions[base + 5]);
                v3 = new THREE.Vector3(positions[base + 6], positions[base + 7], positions[base + 8]);
            }

            // Calculate triangle normal
            const edge1 = new THREE.Vector3().subVectors(v2, v1);
            const edge2 = new THREE.Vector3().subVectors(v3, v1);
            const normal = new THREE.Vector3().crossVectors(edge1, edge2).normalize();

            // Calculate triangle center
            const center = new THREE.Vector3()
                .addVectors(v1, v2)
                .add(v3)
                .divideScalar(3);

            triangles.push({
                index: i,
                vertices: [v1, v2, v3],
                normal: normal,
                center: center,
                used: false
            });
        }

        // Find the clicked triangle
        const clickedTriangle = triangles[clickedTriangleIndex];
        if (!clickedTriangle) return null;

        // Group coplanar triangles starting from the clicked triangle
        const faceTriangles = [clickedTriangle];
        clickedTriangle.used = true;

        const tolerance = 0.01;
        const distanceTolerance = 0.01;

        // Find all triangles coplanar with the clicked triangle
        for (let i = 0; i < triangles.length; i++) {
            if (triangles[i].used) continue;

            const otherTriangle = triangles[i];

            // Check if normals are similar (coplanar)
            const normalDot = Math.abs(clickedTriangle.normal.dot(otherTriangle.normal));
            if (normalDot > (1 - tolerance)) {
                // Check if triangles are on the same plane
                const centerDiff = new THREE.Vector3().subVectors(otherTriangle.center, clickedTriangle.center);
                const distanceToPlane = Math.abs(centerDiff.dot(clickedTriangle.normal));

                if (distanceToPlane < distanceTolerance) {
                    faceTriangles.push(otherTriangle);
                    triangles[i].used = true;
                }
            }
        }

        // Calculate face center and normal
        const faceCenter = new THREE.Vector3();
        faceTriangles.forEach(tri => faceCenter.add(tri.center));
        faceCenter.divideScalar(faceTriangles.length);

        return {
            faceId: `Face_${clickedTriangleIndex}`,
            triangles: faceTriangles,
            normal: clickedTriangle.normal.clone(),
            center: faceCenter
        };
    }

    createFaceHighlight(faceInfo, object) {
        // Create geometry for all triangles in the face
        const vertices = [];

        faceInfo.triangles.forEach(triangle => {
            triangle.vertices.forEach(vertex => {
                vertices.push(vertex.x, vertex.y, vertex.z);
            });
        });

        const faceGeometry = new THREE.BufferGeometry();
        faceGeometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(vertices), 3));
        faceGeometry.computeVertexNormals();

        // Create highlight material
        const highlightMaterial = new THREE.MeshBasicMaterial({
            color: 0xffdd00,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.8,
            depthTest: false
        });

        // Create mesh for the highlighted face
        this.highlightFaceMesh = new THREE.Mesh(faceGeometry, highlightMaterial);

        // Apply object's transformation matrix
        this.highlightFaceMesh.applyMatrix4(object.matrixWorld);

        // Add small offset to prevent z-fighting
        const normal = faceInfo.normal.clone().transformDirection(object.matrixWorld);
        normal.multiplyScalar(0.005);
        this.highlightFaceMesh.position.add(normal);

        // Add to scene
        this.scene.add(this.highlightFaceMesh);
    }

    calculateFaceBounds(triangles, matrixWorld) {
        const bounds = new THREE.Box3();

        triangles.forEach(triangle => {
            triangle.vertices.forEach(vertex => {
                const worldVertex = vertex.clone().applyMatrix4(matrixWorld);
                bounds.expandByPoint(worldVertex);
            });
        });

        return bounds;
    }

    calculateFaceArea(triangles, matrixWorld) {
        let totalArea = 0;

        triangles.forEach(triangle => {
            const v1 = triangle.vertices[0].clone().applyMatrix4(matrixWorld);
            const v2 = triangle.vertices[1].clone().applyMatrix4(matrixWorld);
            const v3 = triangle.vertices[2].clone().applyMatrix4(matrixWorld);

            const edge1 = new THREE.Vector3().subVectors(v2, v1);
            const edge2 = new THREE.Vector3().subVectors(v3, v1);
            const cross = new THREE.Vector3().crossVectors(edge1, edge2);

            totalArea += cross.length() * 0.5;
        });

        return totalArea;
    }

    calculateOriginalFaceDimensions(triangles) {
        // Calculate face dimensions directly from original geometry (no scaling applied)
        const faceBounds = new THREE.Box3();

        // Calculate bounds from triangle vertices in their original geometry space
        triangles.forEach(triangle => {
            triangle.vertices.forEach(vertex => {
                faceBounds.expandByPoint(vertex);
            });
        });

        const faceSize = faceBounds.getSize(new THREE.Vector3());

        // Use geometry dimensions directly - these are the original model dimensions
        // The triangle vertices from geometry.attributes.position are in the original
        // unscaled space as loaded from the OBJ file
        const originalX = faceSize.x;
        const originalY = faceSize.y;
        const originalZ = faceSize.z;

        // For a face, filter out dimensions that are very small (close to 0)
        // These represent the thickness of the face which should be ignored
        const tolerance = 0.001;
        const dimensions = [];

        if (originalX > tolerance) dimensions.push(originalX);
        if (originalY > tolerance) dimensions.push(originalY);
        if (originalZ > tolerance) dimensions.push(originalZ);

        // Sort dimensions in descending order
        dimensions.sort((a, b) => b - a);

        // For debugging: log the calculations
        console.log('Original Face dimension calculation:', {
            faceSize: { x: faceSize.x, y: faceSize.y, z: faceSize.z },
            originalDimensions: { x: originalX, y: originalY, z: originalZ },
            filteredDimensions: dimensions,
            tolerance: tolerance
        });

        return {
            width: dimensions[0] || 0,
            height: dimensions[1] || 0,
            depth: dimensions[2] || 0,
            allDimensions: { x: originalX, y: originalY, z: originalZ }
        };
    }

    calculateOriginalFaceBounds(triangles) {
        // Calculate original face bounds directly from geometry (no scaling applied)
        const faceBounds = new THREE.Box3();

        // Calculate bounds from triangle vertices in their original geometry space
        triangles.forEach(triangle => {
            triangle.vertices.forEach(vertex => {
                faceBounds.expandByPoint(vertex);
            });
        });

        // Use geometry bounds directly - these are the original model bounds
        // The triangle vertices from geometry.attributes.position are in the original
        // unscaled space as loaded from the OBJ file
        const originalMinX = faceBounds.min.x;
        const originalMaxX = faceBounds.max.x;
        const originalMinY = faceBounds.min.y;
        const originalMaxY = faceBounds.max.y;
        const originalMinZ = faceBounds.min.z;
        const originalMaxZ = faceBounds.max.z;

        const sizeX = originalMaxX - originalMinX;
        const sizeY = originalMaxY - originalMinY;
        const sizeZ = originalMaxZ - originalMinZ;

        // For debugging: log the bounds calculation
        console.log('Original Face bounds calculation:', {
            geometryBounds: {
                min: { x: faceBounds.min.x, y: faceBounds.min.y, z: faceBounds.min.z },
                max: { x: faceBounds.max.x, y: faceBounds.max.y, z: faceBounds.max.z }
            },
            originalBounds: {
                min: { x: originalMinX, y: originalMinY, z: originalMinZ },
                max: { x: originalMaxX, y: originalMaxY, z: originalMaxZ }
            },
            originalSizes: { x: sizeX, y: sizeY, z: sizeZ }
        });

        return {
            minX: originalMinX,
            maxX: originalMaxX,
            minY: originalMinY,
            maxY: originalMaxY,
            minZ: originalMinZ,
            maxZ: originalMaxZ,
            sizeX: sizeX,
            sizeY: sizeY,
            sizeZ: sizeZ
        };
    }

    updateInfoDisplay() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            this.originalInfoContent = `
                <div class="occ-style-info">
                    <strong>${this.modelInfo.fileName}</strong>
                    <table class="mt-1 text-xs w-full">
                        <tr>
                            <td class="pr-2">Vertices:</td>
                            <td>${this.modelInfo.vertices.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td class="pr-2">Faces:</td>
                            <td>${this.modelInfo.actualFaces ? this.modelInfo.actualFaces.toLocaleString() : this.modelInfo.faces.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td class="pr-2">Triangles:</td>
                            <td class="text-gray-500">${this.modelInfo.faces.toLocaleString()}</td>
                        </tr>
                    </table>

                </div>
            `;

            infoElement.innerHTML = this.originalInfoContent;
        }
    }

    resetCamera() {
        if (this.currentModel) {
            // Get the current bounding box of the positioned and scaled model
            const box = new THREE.Box3().setFromObject(this.currentModel);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            const maxDim = Math.max(size.x, size.y, size.z);

            // Position camera in isometric view like CAD software
            const distance = maxDim * 2.5;
            this.camera.position.set(
                center.x + distance * 0.7,
                center.y + distance * 0.7,
                center.z + distance * 0.7
            );

            // Look at and orbit around the visual center of the model
            this.camera.lookAt(center);
            this.controls.target.copy(center);

        } else {
            // Default camera position if no model
            this.camera.position.set(15, 10, 15);
            this.camera.lookAt(0, 0, 0);
            this.controls.target.set(0, 0, 0);
        }

        this.controls.update();
    }

    toggleWireframe() {
        if (!this.currentModel) return;

        this.isWireframe = !this.isWireframe;

        this.currentModel.traverse((child) => {
            if (child.isMesh) {
                child.material.wireframe = this.isWireframe;
            }
        });

        // Update button appearance
        const btn = document.getElementById('wireframe-toggle-btn');
        if (btn) {
            btn.style.background = this.isWireframe ? 'rgba(59, 130, 246, 0.9)' : 'rgba(255, 255, 255, 0.9)';
            btn.style.color = this.isWireframe ? 'white' : '#374151';
        }
    }

    downloadCurrentModel() {
        // Download the originally loaded OBJ file
        if (this.originalSourceUrl) {
            const link = document.createElement('a');
            link.href = this.originalSourceUrl;
            link.download = this.modelInfo.fileName || 'model.obj';
            link.click();
            console.log(`Downloading model from original source: ${this.originalSourceUrl}`);
        } else if (this.modelInfo.fileName) {
            // Fallback to previous behavior if originalSourceUrl is not available
            console.warn('Original source URL not available, using fallback path');
            const downloadUrl = `/download/outputs/obj/${this.modelInfo.fileName}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = this.modelInfo.fileName;
            link.click();
        } else {
            console.error('No model filename available for download');
        }
    }

    toggleFullscreen() {
        // Implementation for fullscreen mode
        const container = this.container;
        if (!document.fullscreenElement) {
            container.requestFullscreen().then(() => {
                this.onWindowResize();
            });
        } else {
            document.exitFullscreen();
        }
    }

    toggleCodeOutput() {
        const container = document.getElementById('code-output-container');
        const icon = document.querySelector('#toggle-code-btn i.fa-chevron-down');

        if (container.classList.contains('hidden')) {
            container.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
        } else {
            container.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }
    }

    showLoading(show = true) {
        const loading = document.getElementById('viewer-loading');
        const placeholder = this.container.querySelector('.viewer-placeholder');

        if (show) {
            loading?.classList.remove('hidden');
            placeholder?.classList.add('hidden');
        } else {
            loading?.classList.add('hidden');
        }
    }

    hideLoading() {
        this.showLoading(false);
    }

    showControls() {
        document.getElementById('viewer-controls')?.classList.remove('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.remove('hidden');
        document.getElementById('select-face-btn')?.classList.remove('hidden');
        document.getElementById('select-edge-btn')?.classList.remove('hidden');
    }

    hideControls() {
        document.getElementById('viewer-controls')?.classList.add('hidden');
        document.getElementById('fullscreen-viewer-btn')?.classList.add('hidden');
        document.getElementById('select-face-btn')?.classList.add('hidden');
        document.getElementById('select-edge-btn')?.classList.add('hidden');

        // Reset select face mode when hiding controls
        if (this.isSelectFaceMode) {
            this.isSelectFaceMode = false;
            document.getElementById('select-face-btn')?.classList.remove('select-face-active');
        }
        
        // Reset select edge mode when hiding controls
        if (this.isSelectEdgeMode) {
            this.isSelectEdgeMode = false;
            document.getElementById('select-edge-btn')?.classList.remove('select-edge-active');
        }
    }

    showInfo() {
        document.getElementById('viewer-info')?.classList.remove('hidden');
    }

    showError(message) {
        console.error(`[3D-VIEWER] Error: ${message}`);
        const viewerElement = this.container.querySelector('.viewer-3d');
        const placeholder = viewerElement.querySelector('.viewer-placeholder');
        
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-exclamation-triangle text-red-500"></i>
                <p class="text-sm text-red-100">${message}</p>
                <p class="text-xs opacity-75 mt-1">Check console for details</p>
            `;
            placeholder.classList.remove('hidden');
        }
        
        this.hideLoading();
        
        // Show a notification if possible
        if (window.showNotification) {
            window.showNotification(message, 'error', 5000);
        }
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        if (this.controls) {
            this.controls.update();
        }

        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    // Public method to load a model from URL
    async loadModel(objUrl, fileName) {
        try {
            // Store the original source URL
            this.originalSourceUrl = objUrl;
            
            await this.loadOBJFile(objUrl, fileName);
            return true;
        } catch (error) {
            console.error('[3D-VIEWER] Failed to load model:', error);
            this.showError(`Error: ${error.message || 'Unknown error'}`);
            return false;
        }
    }

    // Clear the current model
    clearModel() {
        if (this.currentModel) {
            this.scene.remove(this.currentModel);
            this.currentModel = null;

            // Reset model center
            this.modelCenter.set(0, 0, 0);
            this.modelBoundingBox = null;
        }

        this.hideControls();

        // Update info to show just controls
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            infoElement.innerHTML = `
                <strong>No model loaded</strong><br>
                <hr class="my-1 border-gray-300">
                <div class="text-xs mt-1">
                    ${this.controlsInfo.left}<br>
                    ${this.controlsInfo.wheel}<br>
                    ${this.controlsInfo.middle}
                </div>
            `;
        }

        // Keep the info panel visible to show controls help
        document.getElementById('viewer-info')?.classList.remove('hidden');

        const placeholder = this.container.querySelector('.viewer-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="fas fa-cube"></i>
                <p class="text-sm">3D model will appear here</p>
                <p class="text-xs opacity-75 mt-1">Generate a model to see the preview</p>
            `;
            placeholder.classList.remove('hidden');
        }
    }

    // Set standard camera views
    setStandardView(viewType) {
        if (!this.currentModel) return;

        // Get current model center and size
        const box = new THREE.Box3().setFromObject(this.currentModel);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z) * 2;

        switch (viewType) {
            case 'top':
                this.camera.position.set(center.x, center.y + maxDim, center.z);
                break;
            case 'bottom':
                this.camera.position.set(center.x, center.y - maxDim, center.z);
                break;
            case 'front':
                this.camera.position.set(center.x, center.y, center.z + maxDim);
                break;
            case 'back':
                this.camera.position.set(center.x, center.y, center.z - maxDim);
                break;
            case 'left':
                this.camera.position.set(center.x - maxDim, center.y, center.z);
                break;
            case 'right':
                this.camera.position.set(center.x + maxDim, center.y, center.z);
                break;
            default:
                return;
        }

        // Look at model center
        this.camera.lookAt(center);
        this.controls.target.copy(center);
        this.controls.update();
    }

    // Helper method to update controls info display
    updateControlsInfoDisplay() {
        if (!this.currentModel) return;

        const box = new THREE.Box3().setFromObject(this.currentModel);
        const center = box.getCenter(new THREE.Vector3());

        const infoText = `
            <div class="text-xs mt-1">
                <b>Controls:</b><br>
                ${this.controlsInfo.left}<br>
                ${this.controlsInfo.wheel}<br>
                ${this.controlsInfo.middle}<br>
                <span style="color: #ffcc00;">Orbit around: ${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)}</span><br>
                <span style="color: #45b6fe;">Ctrl + click: Select edge</span>
            </div>
        `;

        // Append to model info if exists
        const infoElement = document.getElementById('model-info-text');
        if (infoElement && infoElement.innerHTML) {
            const currentInfo = infoElement.innerHTML;
            if (!currentInfo.includes('Controls:')) { // Check for English "Controls:"
                infoElement.innerHTML += `<hr class="my-1 border-gray-300">` + infoText;
            }
        }
    }

    onModelClick(event) {
        if (!this.currentModel || event.button !== 0) return;

        // Use renderer.domElement for consistent coordinate calculation relative to the canvas
        const rect = this.renderer.domElement.getBoundingClientRect();
        const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2(x, y);
        raycaster.setFromCamera(mouse, this.camera);

        const intersects = raycaster.intersectObject(this.currentModel, true);

        // Clear any ongoing flash from a previous selection/state
        if (this.flashIntervalId) {
            clearInterval(this.flashIntervalId);
            this.flashIntervalId = null;
        }

        // Remove previous highlight face if exists
        if (this.highlightFaceMesh) {
            this.scene.remove(this.highlightFaceMesh);
            this.highlightFaceMesh = null;
        }
        
        // Remove previous highlight edge if exists
        if (this.highlightEdgeMesh) {
            this.scene.remove(this.highlightEdgeMesh);
            this.highlightEdgeMesh = null;
        }

        if (intersects.length > 0) {
            const intersection = intersects[0];
            const clickedTriangle = intersection.face;
            const object = intersection.object; // The intersected THREE.Mesh

            if (!clickedTriangle || !object.geometry || !object.geometry.attributes.position) {
                console.warn("Intersected object or face is missing required data for selection.");
                this.updateInfoDisplay();
                return;
            }
            
            // Check if we're in Select Edge Mode or normal edge selection (right-click or modifier key)
            if (this.isSelectEdgeMode || event.ctrlKey) {
                this.handleEdgeSelection(intersection, object);
                return;
            }

            // Find the actual face (group of coplanar triangles) that contains this triangle
            const actualFaceInfo = this.findActualFace(object.geometry, intersection.faceIndex);

            if (!actualFaceInfo) {
                this.updateInfoDisplay();
                return;
            }

            // Create highlight geometry for the entire face
            this.createFaceHighlight(actualFaceInfo, object);

            // Store selected face info for potential use with Select Face button
            this.selectedFaceInfo = actualFaceInfo;

            // Calculate original face dimensions and bounds (no scaling applied)
            const originalBounds = this.calculateOriginalFaceBounds(actualFaceInfo.triangles);

            // Check if we're in Select Face Mode
            if (this.isSelectFaceMode) {
                // In Select Face Mode - paste to chat and exit mode
                this.pasteFaceBoundingBoxToChat();
                this.toggleSelectFaceMode(); // Exit select face mode
                return;
            }

            // Normal face selection display
            const selectedFaceInfoText = `
                <div class="p-2 bg-black text-white rounded text-xs mt-2 border border-gray-700">
                    <b>Selected Face: ${actualFaceInfo.faceId}</b><br>
                    Triangles in Face: ${actualFaceInfo.triangles.length}<br>
                    <div class="mt-1">
                        <b>Face Bounding Box:</b><br>
                        X[${originalBounds.minX.toFixed(1)},${originalBounds.maxX.toFixed(1)}] Y[${originalBounds.minY.toFixed(1)},${originalBounds.maxY.toFixed(1)}] Z[${originalBounds.minZ.toFixed(1)},${originalBounds.maxZ.toFixed(1)}]
                    </div>
                </div>
            `;

            const infoElement = document.getElementById('model-info-text');
            if (infoElement) {
                // Ensure originalInfoContent is up-to-date
                if (!this.originalInfoContent || !this.originalInfoContent.includes(this.modelInfo.fileName) || this.modelInfo.fileName === '') {
                    this.updateInfoDisplay();
                }
                infoElement.innerHTML = this.originalInfoContent + selectedFaceInfoText;
            }

            // Setup flash effect for the highlight
            this.flashHighlightedFace();

        } else {
            // Clicked on empty space, restore original info display
            this.updateInfoDisplay();
        }
    }

    findRealEdges(geometry) {
        const positions = geometry.attributes.position.array;
        const indices = geometry.index ? geometry.index.array : null;
        
        const edgeMap = new Map();
        const edgeTriangleMap = new Map();
        
        const triangleCount = indices ? indices.length / 3 : positions.length / 9;
        console.log(`Triangle count: ${triangleCount}`);
        
        for (let i = 0; i < triangleCount; i++) {
            let a, b, c;
            
            if (indices) {
                a = indices[i * 3];
                b = indices[i * 3 + 1];
                c = indices[i * 3 + 2];
            } else {
                a = i * 3;
                b = i * 3 + 1;
                c = i * 3 + 2;
            }
            
            const va = new THREE.Vector3(
                positions[a * 3], positions[a * 3 + 1], positions[a * 3 + 2]
            );
            const vb = new THREE.Vector3(
                positions[b * 3], positions[b * 3 + 1], positions[b * 3 + 2]
            );
            const vc = new THREE.Vector3(
                positions[c * 3], positions[c * 3 + 1], positions[c * 3 + 2]
            );
            
            const edge1 = new THREE.Vector3().subVectors(vb, va);
            const edge2 = new THREE.Vector3().subVectors(vc, va);
            const normal = new THREE.Vector3().crossVectors(edge1, edge2).normalize();
            
            // Store edge by ascending order of vertex indices
            this.addEdgeInfo(edgeMap, edgeTriangleMap, Math.min(a, b), Math.max(a, b), i, normal);
            this.addEdgeInfo(edgeMap, edgeTriangleMap, Math.min(b, c), Math.max(b, c), i, normal);
            this.addEdgeInfo(edgeMap, edgeTriangleMap, Math.min(c, a), Math.max(c, a), i, normal);
        }
        
        // Find edges on the border or between different planes
        const realEdges = new Set();
        let realEdgeCount = 0;
        let internalEdgeCount = 0;
        
        edgeMap.forEach((count, edgeKey) => {
            // Get information about triangles sharing this edge
            const triangleInfos = edgeTriangleMap.get(edgeKey) || [];
            
            // If only 1 triangle contains this edge, it's a border edge
            if (triangleInfos.length === 1) {
                realEdges.add(edgeKey);
                realEdgeCount++;
                return;
            }
            
            // If there are 2 triangles, check if they are on the same plane
            if (triangleInfos.length === 2) {
                const normal1 = triangleInfos[0].normal;
                const normal2 = triangleInfos[1].normal;
                
                // Calculate the angle between normal vectors
                const angleBetweenNormals = normal1.angleTo(normal2) * (180 / Math.PI);
                
                // If the angle is greater than the threshold (not coplanar), this is a real edge
                if (angleBetweenNormals > 10) {  // 10-degree threshold
                    realEdges.add(edgeKey);
                    realEdgeCount++;
                } else {
                    internalEdgeCount++;
                }
            } else {
                internalEdgeCount++;
            }
        });
        
        console.log(`Total real edges: ${realEdgeCount}, internal edges: ${internalEdgeCount}`);
        
        return realEdges;
    }
    
    addEdgeInfo(edgeMap, edgeTriangleMap, v1, v2, triangleIndex, normal) {
        const edgeKey = `${v1}-${v2}`;
        
        if (!edgeMap.has(edgeKey)) {
            edgeMap.set(edgeKey, 1);
        } else {
            edgeMap.set(edgeKey, edgeMap.get(edgeKey) + 1);
        }
        
        if (!edgeTriangleMap.has(edgeKey)) {
            edgeTriangleMap.set(edgeKey, []);
        }
        edgeTriangleMap.get(edgeKey).push({
            triangleIndex: triangleIndex,
            normal: normal
        });
    }
    
    handleEdgeSelection(intersection, object) {
        const clickedFace = intersection.face;
        const geometry = object.geometry;
        
        if (!clickedFace || !geometry || !geometry.attributes.position) {
            return;
        }
        
        console.log("Triangle click:", clickedFace);
        
        const realEdges = this.findRealEdges(geometry);
        console.log("Number of real edges:", realEdges.size);
        
        // Extract vertices from geometry
        const positions = geometry.attributes.position.array;
        
        // Get face vertices
        const a = new THREE.Vector3(
            positions[clickedFace.a * 3],
            positions[clickedFace.a * 3 + 1],
            positions[clickedFace.a * 3 + 2]
        );
        
        const b = new THREE.Vector3(
            positions[clickedFace.b * 3],
            positions[clickedFace.b * 3 + 1],
            positions[clickedFace.b * 3 + 2]
        );
        
        const c = new THREE.Vector3(
            positions[clickedFace.c * 3],
            positions[clickedFace.c * 3 + 1],
            positions[clickedFace.c * 3 + 2]
        );
        
        // Find the closest edge to the click point
        const clickPoint = intersection.point.clone();
        object.worldToLocal(clickPoint); // Convert to local space
        
        const edges = [];
        
        const edgeAB = `${Math.min(clickedFace.a, clickedFace.b)}-${Math.max(clickedFace.a, clickedFace.b)}`;
        if (realEdges.has(edgeAB)) {
            edges.push({ 
                start: a, 
                end: b, 
                distance: this.pointToLineDistance(clickPoint, a, b),
                id: `Edge_${clickedFace.a}_${clickedFace.b}`,
                vertexIndices: [clickedFace.a, clickedFace.b],
                type: 'real'
            });
            console.log(`Edge ${edgeAB} is a real edge`);
        } else {
            console.log(`Edge ${edgeAB} is NOT a real edge`);
        }
        
        const edgeBC = `${Math.min(clickedFace.b, clickedFace.c)}-${Math.max(clickedFace.b, clickedFace.c)}`;
        if (realEdges.has(edgeBC)) {
            edges.push({ 
                start: b, 
                end: c, 
                distance: this.pointToLineDistance(clickPoint, b, c),
                id: `Edge_${clickedFace.b}_${clickedFace.c}`,
                vertexIndices: [clickedFace.b, clickedFace.c],
                type: 'real'
            });
            console.log(`Edge ${edgeBC} is a real edge`);
        } else {
            console.log(`Edge ${edgeBC} is NOT a real edge`);
        }
        
        const edgeCA = `${Math.min(clickedFace.c, clickedFace.a)}-${Math.max(clickedFace.c, clickedFace.a)}`;
        if (realEdges.has(edgeCA)) {
            edges.push({ 
                start: c, 
                end: a, 
                distance: this.pointToLineDistance(clickPoint, c, a),
                id: `Edge_${clickedFace.c}_${clickedFace.a}`,
                vertexIndices: [clickedFace.c, clickedFace.a],
                type: 'real'
            });
            console.log(`Edge ${edgeCA} is a real edge`);
        } else {
            console.log(`Edge ${edgeCA} is NOT a real edge`);
        }
        
        // If no real edges are found in this triangle
        if (edges.length === 0) {
            console.warn("No real edges found in this triangle");
            return;
        }
        
        // Find closest edge
        edges.sort((e1, e2) => e1.distance - e2.distance);
        const closestEdge = edges[0];
        console.log("Selected edge:", closestEdge);
        
        // Apply object's transformation matrix to get world coordinates
        const startWorld = closestEdge.start.clone().applyMatrix4(object.matrixWorld);
        const endWorld = closestEdge.end.clone().applyMatrix4(object.matrixWorld);
        
        // Calculate original coordinates (unscaled)
        const originalStart = closestEdge.start.clone();
        const originalEnd = closestEdge.end.clone();
        
        // Store selected edge info
        this.selectedEdgeInfo = {
            id: closestEdge.id,
            start: originalStart,
            end: originalEnd,
            startWorld: startWorld,
            endWorld: endWorld,
            vertexIndices: closestEdge.vertexIndices,
            type: closestEdge.type
        };
        
        // Highlight the selected edge
        this.createEdgeHighlight(closestEdge, object);
        
        // If in select edge mode, paste to chat and exit mode
        if (this.isSelectEdgeMode) {
            this.pasteEdgeCoordinatesToChat();
            this.toggleSelectEdgeMode(); // Exit select edge mode
            return;
        }
        
        // Display edge information
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            // Ensure originalInfoContent is up-to-date
            if (!this.originalInfoContent) {
                this.updateInfoDisplay();
            }
            
            const selectedEdgeInfoText = `
                <div class="p-2 bg-black text-white rounded text-xs mt-2 border border-gray-700">
                    <b>Selected Edge: ${closestEdge.id}</b><br>
                    <div class="mt-1">
                        <b>Original Coordinates:</b><br>
                        Start: (${originalStart.x.toFixed(2)}, ${originalStart.y.toFixed(2)}, ${originalStart.z.toFixed(2)})<br>
                        End: (${originalEnd.x.toFixed(2)}, ${originalEnd.y.toFixed(2)}, ${originalEnd.z.toFixed(2)})
                    </div>
                </div>
            `;
            
            infoElement.innerHTML = this.originalInfoContent + selectedEdgeInfoText;
        }
        
        // Flash the highlighted edge
        this.flashHighlightedEdge();
    }
    
    // Calculate distance from point to line segment
    pointToLineDistance(point, lineStart, lineEnd) {
        const line = new THREE.Line3(lineStart, lineEnd);
        const closestPoint = new THREE.Vector3();
        line.closestPointToPoint(point, true, closestPoint);
        return point.distanceTo(closestPoint);
    }
    
    // Create highlight for selected edge
    createEdgeHighlight(edge, object) {
        // Create geometry for the edge line
        const geometry = new THREE.BufferGeometry();
        const vertices = new Float32Array([
            edge.start.x, edge.start.y, edge.start.z,
            edge.end.x, edge.end.y, edge.end.z
        ]);
        
        geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
        
        // Create line material
        const material = new THREE.LineBasicMaterial({
            color: 0xffdd00,
            linewidth: 3,
            depthTest: false
        });
        
        // Create line mesh
        this.highlightEdgeMesh = new THREE.Line(geometry, material);
        
        // Apply object's transformation matrix
        this.highlightEdgeMesh.applyMatrix4(object.matrixWorld);
        
        // Add to scene
        this.scene.add(this.highlightEdgeMesh);
    }
    
    // Flash the highlighted edge
    flashHighlightedEdge() {
        if (!this.highlightEdgeMesh) return;
        
        let flashCount = 0;
        const highlightMaterial = this.highlightEdgeMesh.material;
        const originalColor = highlightMaterial.color.clone();
        
        this.flashIntervalId = setInterval(() => {
            if (!this.highlightEdgeMesh) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                return;
            }
            
            // Stop after a few flashes
            if (flashCount >= 4) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                highlightMaterial.color.copy(originalColor);
                return;
            }
            
            // Toggle color for flash effect
            if (flashCount % 2 === 0) {
                highlightMaterial.color.setHex(0xffffff);
            } else {
                highlightMaterial.color.copy(originalColor);
            }
            
            flashCount++;
        }, 150);
    }

    // Flash the highlighted face
    flashHighlightedFace() {
        if (!this.highlightFaceMesh) return;

        let flashCount = 0;
        const highlightMaterial = this.highlightFaceMesh.material;
        const originalOpacity = highlightMaterial.opacity;

        this.flashIntervalId = setInterval(() => {
            if (!this.highlightFaceMesh) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                return;
            }

            // Stop after a few flashes
            if (flashCount >= 4) {
                clearInterval(this.flashIntervalId);
                this.flashIntervalId = null;
                highlightMaterial.opacity = originalOpacity;
                return;
            }

            // Toggle opacity for flash effect
            if (flashCount % 2 === 0) {
                highlightMaterial.opacity = 0.3; // Dim
            } else {
                highlightMaterial.opacity = originalOpacity; // Normal
            }

            flashCount++;
        }, 150);
    }

    // Toggle Select Edge Mode
    toggleSelectEdgeMode() {
        // Exit face selection mode if active
        if (this.isSelectFaceMode) {
            this.toggleSelectFaceMode();
        }
        
        this.isSelectEdgeMode = !this.isSelectEdgeMode;
        const selectEdgeBtn = document.getElementById('select-edge-btn');
        
        if (this.isSelectEdgeMode) {
            // Enable select edge mode
            selectEdgeBtn?.classList.add('select-edge-active');
            this.showSelectEdgeModeMessage();
        } else {
            // Disable select edge mode
            selectEdgeBtn?.classList.remove('select-edge-active');
            this.hideSelectEdgeModeMessage();
            this.selectedEdgeInfo = null;
        }
    }
    
    // Show message when in select edge mode
    showSelectEdgeModeMessage() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            const selectModeMessage = `
                <div class="p-2 bg-blue-600 text-white rounded text-xs mt-2">
                    <b><i class="fas fa-project-diagram mr-1"></i>Select Edge Mode Active</b><br>
                    Click on any edge to select it for chat
                </div>
            `;
            
            // Store original content if not already stored
            if (!this.originalInfoContent || !this.originalInfoContent.includes(this.modelInfo.fileName)) {
                this.updateInfoDisplay();
            }
            
            infoElement.innerHTML = this.originalInfoContent + selectModeMessage;
        }
    }
    
    // Hide select edge mode message
    hideSelectEdgeModeMessage() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement && this.originalInfoContent) {
            infoElement.innerHTML = this.originalInfoContent;
        }
    }
    
    // Paste Edge Coordinates to Chat
    pasteEdgeCoordinatesToChat() {
        if (!this.selectedEdgeInfo) {
            console.warn('No edge selected to paste to chat');
            return;
        }
        
        // Format the Edge Coordinates information
        const start = this.selectedEdgeInfo.start;
        const end = this.selectedEdgeInfo.end;
        const edgeCoordinatesText = `Edge Coordinates: Start(${start.x.toFixed(1)}, ${start.y.toFixed(1)}, ${start.z.toFixed(1)}) End(${end.x.toFixed(1)}, ${end.y.toFixed(1)}, ${end.z.toFixed(1)})`;
        
        // Get the chat input element
        const userInput = document.getElementById('user-input');
        
        if (userInput) {
            // Add the text to the current input value
            const currentValue = userInput.value.trim();
            const newValue = currentValue ? `${currentValue} ${edgeCoordinatesText}` : edgeCoordinatesText;
            
            userInput.value = newValue;
            
            // Trigger input event to update UI
            userInput.dispatchEvent(new Event('input'));
            
            // Focus on the input
            userInput.focus();
            
            // Position cursor at the end
            userInput.setSelectionRange(newValue.length, newValue.length);
            
            console.log('Edge Coordinates pasted to chat:', edgeCoordinatesText);
        } else {
            console.error('Chat input element not found');
        }
    }

    // Toggle Select Face Mode
    toggleSelectFaceMode() {
        this.isSelectFaceMode = !this.isSelectFaceMode;
        const selectFaceBtn = document.getElementById('select-face-btn');

        if (this.isSelectFaceMode) {
            // Enable select face mode
            selectFaceBtn?.classList.add('select-face-active');
            this.showSelectFaceModeMessage();
        } else {
            // Disable select face mode
            selectFaceBtn?.classList.remove('select-face-active');
            this.hideSelectFaceModeMessage();
            this.selectedFaceInfo = null;
        }
    }

    // Show message when in select face mode
    showSelectFaceModeMessage() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement) {
            const selectModeMessage = `
                <div class="p-2 bg-green-600 text-white rounded text-xs mt-2">
                    <b><i class="fas fa-mouse-pointer mr-1"></i>Select Face Mode Active</b><br>
                    Click on any face to select it for chat
                </div>
            `;

            // Store original content if not already stored
            if (!this.originalInfoContent || !this.originalInfoContent.includes(this.modelInfo.fileName)) {
                this.updateInfoDisplay();
            }

            infoElement.innerHTML = this.originalInfoContent + selectModeMessage;
        }
    }

    // Hide select face mode message
    hideSelectFaceModeMessage() {
        const infoElement = document.getElementById('model-info-text');
        if (infoElement && this.originalInfoContent) {
            infoElement.innerHTML = this.originalInfoContent;
        }
    }

    // Paste Face Bounding Box to Chat
    pasteFaceBoundingBoxToChat() {
        if (!this.selectedFaceInfo) {
            console.warn('No face selected to paste to chat');
            return;
        }

        // Calculate original bounds for the selected face
        const originalBounds = this.calculateOriginalFaceBounds(this.selectedFaceInfo.triangles);

        // Format the Face Bounding Box information
        const faceBoundingBoxText = `Face Bounding Box: X[${originalBounds.minX.toFixed(1)},${originalBounds.maxX.toFixed(1)}] Y[${originalBounds.minY.toFixed(1)},${originalBounds.maxY.toFixed(1)}] Z[${originalBounds.minZ.toFixed(1)},${originalBounds.maxZ.toFixed(1)}]`;

        // Get the chat input element
        const userInput = document.getElementById('user-input');

        if (userInput) {
            // Add the text to the current input value
            const currentValue = userInput.value.trim();
            const newValue = currentValue ? `${currentValue} ${faceBoundingBoxText}` : faceBoundingBoxText;

            userInput.value = newValue;

            // Trigger input event to update UI
            userInput.dispatchEvent(new Event('input'));

            // Focus on the input
            userInput.focus();

            // Position cursor at the end
            userInput.setSelectionRange(newValue.length, newValue.length);

            console.log('Face Bounding Box pasted to chat:', faceBoundingBoxText);
        } else {
            console.error('Chat input element not found');
        }
    }

    // Setup grid
    setupGrid() {
        // Grid removed - method empty but kept for compatibility
    }
}

// Export for use in main.js
window.ModelViewer3D = ModelViewer3D;